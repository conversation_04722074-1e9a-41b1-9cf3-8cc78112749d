# 闲鱼数据处理 - 纯函数版本使用说明

## 🎯 概述

这是专为影刀RPA设计的纯函数版本，**完全没有类定义**，所有功能都是顶级函数，确保影刀能够正确识别和调用。

## 📁 文件说明

- **`xianyu_functions_pure.py`** - 纯函数版本主文件，影刀直接调用这个文件中的函数

## 🔧 影刀专用函数

### 1. `yingdao_process_homepage(response_body_list, output_file=None)`
**功能**: 处理首页数据并导出Excel
**参数**:
- `response_body_list`: 影刀返回的响应体列表
- `output_file`: 输出Excel文件名（可选）

**返回**: 处理结果字典
```python
{
    'success': True,
    'count': 1,  # 商品总数
    'file': 'output.xlsx',  # 输出文件路径
    'statistics': {
        'new_products': 0,     # 新增商品数
        'updated_products': 1,  # 更新商品数
        'marked_for_details': 1 # 标记获取详情数
    }
}
```

### 2. `yingdao_get_detail_ids()`
**功能**: 获取需要详情页数据的商品ID列表
**返回**: 商品ID列表
```python
['123456789', '987654321']
```

### 3. `yingdao_reset_data()`
**功能**: 重置数据处理器状态
**返回**: 布尔值（是否成功重置）

### 4. `yingdao_get_stats()`
**功能**: 获取当前商品统计信息
**返回**: 统计信息字典
```python
{
    'total_count': 10,    # 总商品数
    'marked_count': 3     # 标记获取详情的商品数
}
```

## 🚀 影刀调用示例

### 基本使用流程

```python
# 1. 处理首页数据
result = yingdao_process_homepage(response_data, "商品数据.xlsx")
print(f"处理完成，共 {result['count']} 个商品")

# 2. 获取需要详情的商品ID
detail_ids = yingdao_get_detail_ids()
print(f"需要获取详情的商品: {len(detail_ids)} 个")

# 3. 查看统计信息
stats = yingdao_get_stats()
print(f"总商品: {stats['total_count']}, 标记: {stats['marked_count']}")

# 4. 重置数据（可选）
reset_success = yingdao_reset_data()
```

### 批量处理流程

```python
# 处理多批数据（自动去重）
for i, batch_data in enumerate(all_batches):
    result = yingdao_process_homepage(batch_data, f"商品数据_批次{i+1}.xlsx")
    print(f"批次 {i+1}: 新增 {result['statistics']['new_products']}, 更新 {result['statistics']['updated_products']}")

# 最后获取所有需要详情的商品ID
all_detail_ids = yingdao_get_detail_ids()
```

## ✨ 核心特性

### ✅ 自动去重
- 基于商品ID自动去重
- 相同ID的商品会更新而不是重复添加
- 跨批次保持数据状态

### ✅ 智能标记
- 自动标记需要获取详情的商品
- 标记条件：
  - 想要人数 > 5人
  - 发布时间 ≤ 24/48/72小时
  - 发布时间 ≤ 1周

### ✅ 完整日志
- 详细的处理过程日志
- 自动生成与Excel文件同名的日志文件
- 包含统计信息和错误处理

### ✅ 状态管理
- 支持重置数据状态
- 支持查看当前统计
- 支持跨批次累积处理

## 🧪 测试验证

### 基本功能测试
```bash
python xianyu_functions_pure.py
```

### 去重功能验证
测试结果显示：
- ✅ 第一批：新增 1 个商品
- ✅ 第二批：更新 1 个商品（相同ID，价格从6800更新为6500）
- ✅ 总商品数始终为 1（正确去重）

## 📊 影刀可识别的所有函数

```
✅ check_need_details()      - 检查是否需要详情
✅ export_to_excel()         - 导出Excel
✅ extract_label_data()      - 提取标签数据
✅ get_detail_ids()          - 获取详情ID列表
✅ get_stats()               - 获取统计信息
✅ parse_bargain_count()     - 解析小刀价人数
✅ parse_time_info()         - 解析时间信息
✅ parse_want_count()        - 解析想要人数
✅ process_card_data()       - 处理卡片数据
✅ process_homepage()        - 处理首页数据
✅ reset_data()              - 重置数据
✅ setup_logger()            - 设置日志
✅ yingdao_get_detail_ids()  - 影刀专用-获取详情ID
✅ yingdao_get_stats()       - 影刀专用-获取统计
✅ yingdao_process_homepage() - 影刀专用-处理首页
✅ yingdao_reset_data()      - 影刀专用-重置数据
```

## 🎉 优势对比

### 之前的类版本问题
- ❌ 影刀无法识别类中的方法
- ❌ 需要复杂的包装函数
- ❌ 代码结构复杂

### 现在的纯函数版本
- ✅ 影刀直接识别所有函数
- ✅ 代码结构简单清晰
- ✅ 全局状态管理
- ✅ 完整功能保留

## 🔧 注意事项

1. **全局状态**: 使用全局变量 `products_dict` 保存商品数据
2. **函数命名**: 影刀专用函数以 `yingdao_` 开头
3. **参数简化**: 影刀专用函数参数更简单，返回值更直接
4. **错误处理**: 所有函数都包含完善的错误处理和日志记录
5. **商品链接**: 自动组装为正确的闲鱼格式：`https://www.goofish.com/item?id={商品ID}&categoryId={分类ID}`

## 📊 输出文件格式

- **Excel文件** - 保存到当前工作目录
- **日志文件** - 与Excel同名的.log文件
- **自动格式化** - 包含筛选器和样式
- **商品链接** - 正确的闲鱼商品页面链接格式

现在您可以直接在影刀中调用这些函数，无需担心识别问题！
