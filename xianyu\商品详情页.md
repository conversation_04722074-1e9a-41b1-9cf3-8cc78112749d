## 详情页
![alt text](image.png)



### 调用接口1

#### 请求参数

https://h5api.m.goofish.com/h5/mtop.taobao.idle.item.web.recommend.list/1.0/?jsv=2.7.2&appKey=********&t=*************&sign=e0bf799b949af4f41d7cd916dc027241&v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.taobao.idle.item.web.recommend.list&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.item.0.0

#### 响应参数

{
    "api": "mtop.taobao.idle.item.web.recommend.list",
    "data": {
        "cardList": [
            {
                "cardData": {
                    "color": "#FFFC5A54",
                    "height": "56",
                    "iconHeight": "20",
                    "iconUrl": "http://gw.alicdn.com/mt/TB1k.qTgkvoK1RjSZPfXXXPKFXa-64-64.png",
                    "iconWidth": "20",
                    "title": "为你推荐"
                },
                "cardType": 61501,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "秦皇岛",
                    "clickParam": {
                        "args": {
                            "globalBizCode": "autotrade",
                            "zhimaOffline": "false",
                            "matchType": "MNR_SEARCH",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "117",
                            "id": "840802389773",
                            "guessYouLikeExperimentBucketId": "18",
                            "scm": "1007.12711.395734.0",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1728015558000",
                            "strategyName": "guess_u_like",
                            "item_id": "840802389773",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "24",
                            "ext_info": "recallType:MNR_SEARCH;fromScene:;firstSource:;matchType:MNR_SEARCH;itemId:840802389773;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:-1;triggerType:-1;rtfeature#exp_pvk209:1+211:1;ch4CateId#*********;ch1CateId#201155301;matchTypeSet:MNR_SEARCH;ctr:0.09086978;cvr:0.02359930;rsc:0.05724637;scr:0.04800209;x2isc:96.25709;msc:96.25708771;text_score:0.78416;reserve_price:2.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:0;localIndex:0;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "840802389773",
                            "xGlobalBizCode": "idleShop|pinGroup|c2c",
                            "triggerItemId": "************",
                            "position": "24",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_41\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"¥1000\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"117人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n包括：学生用书+教师用书+音频+练习册+Super Grammar练习册+DVD+白板软件\n\nppt课件全包含入门级和1-6级\n\n适用于国内6-12岁的小学生学习。《Super Minds》围绕6个维度： 单词、语法、故事和重点发音、听说读写技能，思维能力、跨学科学习。\n\n[超便宜]【福利价】99r加入小店会员，所有在售商品全部打包！包更新！！只要小店上新网盘同步更新！\n\n\n虚拟产品一经发货不予退款\n\n（免责声明）\n1.所有文件均获取自网络公开渠道，仅供学习和交流使用，所有版权归版权人所有。\n2.如版权方认为侵犯了您的版权，请及时联系删除\n3.本店所有商品售价仅是对资源查找以及整理的加工费用，如喜欢作品，请支持正版哦~\n4.本店所有商品不涉及商用，仅是以众筹的于提供给想要学习的极少部分人用于学习",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "41",
                                        "isStrikeThrough": "true",
                                        "lineHeight": "1.0",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "¥1000",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "¥1000"
                                        },
                                        "arg1": "99_tag_r3_41"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "117人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "117人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1364,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN011pPlFI2Bm61lkYEd8_!!53-fleamarket.heic",
                        "widthSize": 1920
                    },
                    "itemId": "840802389773",
                    "price": "2",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=840802389773",
                    "title": "【全】《Super Minds》 全套资源",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN0193Zv9a2Bm634vZlQj_!!*******************-0-mtopupload.jpg",
                        "userNick": "露露资料"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "广州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "16",
                            "id": "895774434652",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1741331372000",
                            "strategyName": "guess_u_like",
                            "item_id": "895774434652",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "11",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:895774434652;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#-1;ch1CateId#0;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.07990155;cvr:0.04169574;rsc:0.08648492;scr:0.05760274;x2isc:0.19050;msc:0.19050001;text_score:0.85508;reserve_price:1.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:1;localIndex:1;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "895774434652",
                            "triggerItemId": "************",
                            "position": "11",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"16人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " Reading Future++教材综合教程少儿小学英语阅读教程线上 [红圆]电子版+Compass+Reading+Future+1-7级别+含学生书+音频+答案+测试+单词列表，百度pan发。  [红圆]价格1-7级全部打包。  美国...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "16人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "16人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 739,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01s7A7RM24lubG90zEo_!!*******************-0-fleamarket.jpg",
                        "widthSize": 799
                    },
                    "itemId": "895774434652",
                    "price": "1",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=895774434652",
                    "title": "Compass 阅读训练电子版",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01xBVEt324lubfJaVz5_!!*******************-0-mtopupload.jpg",
                        "userNick": "拍下自动发货"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "REDIRECT_CHAT",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "33",
                            "id": "948609830347",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1751788032000",
                            "strategyName": "guess_u_like",
                            "item_id": "948609830347",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "8",
                            "ext_info": "recallType:REDIRECT_CHAT;fromScene:;firstSource:;matchType:REDIRECT_CHAT;itemId:948609830347;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:948609830347;triggerType:XY_ITEM_CHAT;rtfeature#detail_pvk211:1;ch4CateId#*********;ch1CateId#201155301;triggerSet:948609830347%3AXY_ITEM_CHAT;matchTypeSet:REDIRECT_CHAT;ctr:0.10138157;cvr:0.01243168;rsc:0.03556385;scr:0.04404853;x2isc:5.20200;msc:5.20200014;text_score:0.75951;reserve_price:9.99000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:2;localIndex:2;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "948609830347",
                            "triggerItemId": "************",
                            "position": "8",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"33人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n\n[红圆]电子资料内容包含：\n\n[红圆]1AB-6AB 学生书+音频+教师书【text book】\n[红圆]1AB-6AB 练习册+音频+教师书【work book】\n[红圆]1AB-6AB 语法书+教师书【grammar book】\n[红圆]1AB-6AB 听力练习册+教师书+听力文本【listening book】\n[红圆]1AB-6AB 阅读练习册+教师书【reading book】\n[红圆]1AB-6AB 白版软件",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "33人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "33人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1316,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01ZgN2aV1dSDyre657J_!!*******************-53-fleamarket.heic",
                        "widthSize": 1440
                    },
                    "itemId": "948609830347",
                    "price": "9.99",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=948609830347",
                    "title": "朗文少儿英语Primary Longman Elect",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i2/O1CN01EqHYaS1dSDyPhAkPX_!!*******************-0-mtopupload.jpg",
                        "userNick": "翰林书苑"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "RANK_PAY_I2I_HOUR",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "4",
                            "id": "877561622070",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1737016804000",
                            "strategyName": "guess_u_like",
                            "item_id": "877561622070",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "1",
                            "ext_info": "recallType:RANK_PAY_I2I_HOUR;fromScene:;firstSource:;matchType:RANK_PAY_I2I_HOUR;itemId:877561622070;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#-1;ch1CateId#0;triggerSet:************%3AMAIN_ITEM;matchTypeSet:RANK_PAY_I2I_HOUR;ctr:0.07556200;cvr:0.03277496;rsc:0.06493601;scr:0.04309355;x2isc:0.08080;msc:0.08079647;text_score:0.81184;reserve_price:0.99000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:3;localIndex:3;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "877561622070",
                            "triggerItemId": "************",
                            "position": "1",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"4人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "n Level AA-I     ✅【购买须知】 无客服在线，非纸质，电子版资源，拍下百度网盘自动秒发！ 电子资料具有可复制性，一旦发货，不退不换，感谢理解 ！",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "4人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "4人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1334,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01VO0C751QMBiIWdJxS_!!4611686018427382889-0-fleamarket.jpg",
                        "widthSize": 750
                    },
                    "itemId": "877561622070",
                    "price": "0.99",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=877561622070",
                    "title": "英语学习素材阅读理解Reading Comprehensio",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/12921961/O1CN013nR3l91QMBOdAOxLi_!!12921961-0-ggpersonal.jpg",
                        "userNick": "默默"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_U2I2I_pay",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "36",
                            "id": "949792434750",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1752040968000",
                            "strategyName": "guess_u_like",
                            "item_id": "949792434750",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "18",
                            "ext_info": "recallType:SWING_U2I2I_pay;fromScene:;firstSource:;matchType:SWING_U2I2I_pay;itemId:949792434750;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:942520016829;triggerType:ITEM_LT;rtfeature#exp_pvk209:1+210:1+211:7;ch4CateId#*********;ch1CateId#201155301;triggerSet:942520016829%3AITEM_LT%2C934701685319%3AITEM_RT;matchTypeSet:SWING_U2I2I_pay;ctr:0.09481722;cvr:0.01428196;rsc:0.03764708;scr:0.04289149;x2isc:0.11192;msc:0.11191582;text_score:0.63364;reserve_price:6.60000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:4;localIndex:4;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "949792434750",
                            "triggerItemId": "************",
                            "position": "18",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"36人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用优秀\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "   包含有以下科目（年份） [火][火] 数学真题（中+英）更新到24年 数学延伸单元一真题（中+英）更新至24年 数学延伸单元二真题（中+英）更新至24年 化学真题（中+英）更新至24年 经济真题（中+英）更新至24年 生物真题（中+英...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "36人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "36人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用优秀",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用优秀"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1656,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01ydovf81YGhwZ5geXx_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1244
                    },
                    "itemId": "949792434750",
                    "price": "6.60",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=949792434750",
                    "title": "HK香港DSE历年真题年数理化英生考试历年考题+答案+解析",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01IRoLpN1YGht4O2py4_!!*******************-0-mtopupload.jpg",
                        "userNick": "聪聪学习站"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "重庆",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_U2I2I_pay",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "45",
                            "id": "945679698343",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1751164709000",
                            "strategyName": "guess_u_like",
                            "item_id": "945679698343",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "10",
                            "ext_info": "recallType:SWING_U2I2I_pay;fromScene:;firstSource:;matchType:SWING_U2I2I_pay;itemId:945679698343;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:942520016829;triggerType:ITEM_LT;rtfeature#exp_pvk209:1+210:1+211:5;ch4CateId#201459411;ch1CateId#201451101;triggerSet:942520016829%3AITEM_LT;matchTypeSet:SWING_U2I2I_pay;ctr:0.10610247;cvr:0.01757687;rsc:0.05086784;scr:0.04265358;x2isc:0.11247;msc:0.11247348;text_score:0.76842;reserve_price:2.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:5;localIndex:5;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "945679698343",
                            "triggerItemId": "************",
                            "position": "10",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_598\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"累计降价33%\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"45人想要\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "  [红圆]资料内容包含：  [右]1AB-6AB 学生书+音频+教师书【text book】 [右]1AB-6AB 练习册+音频+教师书【work book】 [右]1AB-6AB 语法书+教师书【grammar book】 [右]1AB...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#FF4400",
                                        "size": "12",
                                        "labelId": "598",
                                        "lineHeight": "1.3",
                                        "type": "gradientImageText",
                                        "content": "累计降价33%",
                                        "leftImage": {
                                            "marginRight": "3",
                                            "width": "6",
                                            "url": "https://gw.alicdn.com/imgextra/i4/O1CN01jkcmvf1jktWeur5JU_!!6000000004587-2-tps-18-33.png",
                                            "height": "11"
                                        }
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "累计降价33%"
                                        },
                                        "arg1": "99_tag_r3_598"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "45人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "45人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1440,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01KnnHsM1xEkdukcjsM_!!4611686018427380044-0-fleamarket.jpg",
                        "widthSize": 1440
                    },
                    "itemId": "945679698343",
                    "price": "2",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=945679698343",
                    "title": "朗文少儿英语Primary Longman Elect",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "呵呵哒哒"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "广州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "QE_SWING_I2I",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "1",
                            "id": "951521480694",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1752142986000",
                            "strategyName": "guess_u_like",
                            "item_id": "951521480694",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "22",
                            "ext_info": "recallType:QE_SWING_I2I;fromScene:;firstSource:;matchType:QE_SWING_I2I;itemId:951521480694;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#127812005;ch1CateId#127818002;triggerSet:************%3AMAIN_ITEM;matchTypeSet:QE_SWING_I2I;ctr:0.09667733;cvr:0.00609753;rsc:0.01860441;scr:0.04070707;x2isc:0.44395;msc:0.44395000;text_score:0.88274;reserve_price:119.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:6;localIndex:6;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "951521480694",
                            "triggerItemId": "************",
                            "position": "22",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_41\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"¥415\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"1人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "LLS FOR LISTENING 1,2,3》 包邮！（非边远包邮） Compass出版社 全新 蓝色一套三本/不单卖，通常适合小学高年级",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "41",
                                        "isStrikeThrough": "true",
                                        "lineHeight": "1.0",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "¥415",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "¥415"
                                        },
                                        "arg1": "99_tag_r3_41"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "1人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "1人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1072,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN019zZapK2JWzyMe32q6_!!4611686018427387174-53-fleamarket.heic",
                        "widthSize": 1008
                    },
                    "itemId": "951521480694",
                    "price": "119",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=951521480694",
                    "title": "美国原版进口 全新 少儿英语听力《EXPANDING SKI",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i3/O1CN010RpZAo2JWzfZfmfke_!!0-mtopupload.jpg",
                        "userNick": "萧萧先生"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "RANK_PAY_U2I2I_HOUR",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "3",
                            "id": "956669267362",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1753529348000",
                            "strategyName": "guess_u_like",
                            "item_id": "956669267362",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "5",
                            "ext_info": "recallType:RANK_PAY_U2I2I_HOUR;fromScene:;firstSource:;matchType:RANK_PAY_U2I2I_HOUR;itemId:956669267362;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:943252955587;triggerType:ITEM_LT;rtfeature#exp_pvk211:1;ch4CateId#*********;ch1CateId#201155301;triggerSet:943252955587%3AITEM_LT;matchTypeSet:RANK_PAY_U2I2I_HOUR;ctr:0.07583532;cvr:0.01805532;rsc:0.03726419;scr:0.04037408;x2isc:0.07914;msc:0.07913814;text_score:0.77170;reserve_price:5.26000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:7;localIndex:7;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "956669267362",
                            "triggerItemId": "************",
                            "position": "5",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"3人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n\n源 DSE初中三年\n\nDSE初中英语英文雅集 \n香港Aristo Sprint(2023 Ed.) 初中英语\n\n[火]资料详情:\n[右]课件PPT\n[右]学生book电孓版\n[右]测试\n[右]音视频\n[右]单词表\n[右]教师book电孓版\n[右]答案\n[右]语法词汇\n[右]任务整合\n\n[红圆]拍下自动发货,发度盘\n[红圆]虚拟物品一旦发出不退不换，看清详情再下单\n\n\n#资料\n发的是 网 盘 链 接，虚拟产品已经发货，概不退款哦！\n\n943252955587\n",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "3人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "3人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1324,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01xYUiSv1l2akKCW65X_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1428
                    },
                    "itemId": "956669267362",
                    "price": "5.26",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=956669267362",
                    "title": "香港英语Aristo Sprint全套教学课件PPT测试等资",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i3/O1CN01krDOSH1l2aifqDU8W_!!*******************-0-mtopupload.jpg",
                        "userNick": "森林明亮的深海兔鱼"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "广州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "RANK_PAY_I2I_HOUR",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "6",
                            "id": "934932623850",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1749264192000",
                            "strategyName": "guess_u_like",
                            "item_id": "934932623850",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "14",
                            "ext_info": "recallType:RANK_PAY_I2I_HOUR;fromScene:;firstSource:;matchType:RANK_PAY_I2I_HOUR;itemId:934932623850;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#126864808;ch1CateId#126867000;triggerSet:************%3AMAIN_ITEM;matchTypeSet:RANK_PAY_I2I_HOUR;ctr:0.07488266;cvr:0.02136889;rsc:0.04299929;scr:0.04013069;x2isc:0.05575;msc:0.05574707;text_score:0.75864;reserve_price:2.90000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:8;localIndex:8;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "934932623850",
                            "triggerItemId": "************",
                            "position": "14",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_1017\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"可小刀\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"6人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用优秀\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " 包含：194期音频MP3，每期15-20分钟，带67个PDF文本，适合8岁以上。✅拍下秒发 用讲故事的方式趣说科学！从新闻, 科普到文史，全球极受欢迎的儿童播客节目, 带孩子打开新世界的大门。 节目的语速不快，比较适合喜欢科学的英语学习者...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "labelId": "1017",
                                        "width": "50",
                                        "type": "img",
                                        "content": "可小刀",
                                        "url": "https://gw.alicdn.com/imgextra/i2/O1CN01fttvvl1LBaPWf4uuH_!!6000000001261-2-tps-150-48.png",
                                        "height": "16",
                                        "marginLeft": "2"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "可小刀"
                                        },
                                        "arg1": "99_tag_r3_1017"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "6人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "6人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用优秀",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用优秀"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1080,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01IGykdJ1DwHxdeMLXD_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1080
                    },
                    "itemId": "934932623850",
                    "price": "2.90",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=934932623850",
                    "title": "儿童英语播客Tumble Science 用讲故事方式讲科学",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i3/O1CN01byuFxD1DwHxc8FfNu_!!*******************-0-mtopupload.jpg",
                        "userNick": "每天努力一点点"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "RANK_PAY_U2I2I",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "5",
                            "id": "929157346550",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1748187104000",
                            "strategyName": "guess_u_like",
                            "item_id": "929157346550",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "26",
                            "ext_info": "recallType:RANK_PAY_U2I2I;fromScene:;firstSource:;matchType:RANK_PAY_U2I2I;itemId:929157346550;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:943252955587;triggerType:ITEM_LT;rtfeature#null;ch4CateId#-1;ch1CateId#0;triggerSet:943252955587%3AITEM_LT;matchTypeSet:RANK_PAY_U2I2I;ctr:0.07693768;cvr:0.02964750;rsc:0.06010275;scr:0.04003106;x2isc:0.11837;msc:0.11837288;text_score:0.80484;reserve_price:1.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:9;localIndex:9;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "929157346550",
                            "triggerItemId": "************",
                            "position": "26",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"5人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "生书、音频、词汇表、测试卷、闪卡和答案。适合小学至初中，助力21世纪技能培养。内容涵盖科学、社会、人文等，提升孩子的视野和阅读能力。   来自Compass的优质系列，内容新颖，操作实用。每个阶段设计细致，从Starter到Create共6...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "5人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "5人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1284,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01hRFdmQ1uOsYbV0fVi_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1292
                    },
                    "itemId": "929157346550",
                    "price": "1",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=929157346550",
                    "title": "全套《Reading Future》电子资料，包含1-7级学",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i2/O1CN01sGYAMT1uOsYWOGpSO_!!*******************-0-mtopupload.jpg",
                        "userNick": "果壳格格"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "MNR_SEARCH",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "1",
                            "id": "957344089168",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1753441309000",
                            "strategyName": "guess_u_like",
                            "item_id": "957344089168",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "19",
                            "ext_info": "recallType:MNR_SEARCH;fromScene:;firstSource:;matchType:MNR_SEARCH;itemId:957344089168;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:-1;triggerType:-1;rtfeature#null;ch4CateId#*********;ch1CateId#201155301;matchTypeSet:MNR_SEARCH;ctr:0.18678620;cvr:0.00506032;rsc:0.03110137;scr:0.03984822;x2isc:25.54878;msc:25.54878044;text_score:0.46986;reserve_price:12.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:10;localIndex:10;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "957344089168",
                            "triggerItemId": "************",
                            "position": "19",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"1人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用优秀\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " 高清资料，110页。无水印，可直接打印。中一～中六适用。 网盘或邮箱发货。 拍下不退不换。",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "1人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "1人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用优秀",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用优秀"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1656,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01KkrCWL1YGhwsIT7LT_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1244
                    },
                    "itemId": "957344089168",
                    "price": "12",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=957344089168",
                    "title": "香港DSE英语高频词汇整理笔记，非纸质",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01IRoLpN1YGht4O2py4_!!*******************-0-mtopupload.jpg",
                        "userNick": "聪聪学习站"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "MNR_SEARCH",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "6",
                            "id": "956683126772",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1753438327000",
                            "strategyName": "guess_u_like",
                            "item_id": "956683126772",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "17",
                            "ext_info": "recallType:MNR_SEARCH;fromScene:;firstSource:;matchType:MNR_SEARCH;itemId:956683126772;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:-1;triggerType:-1;rtfeature#exp_pvk210:1+211:2-clk_pvk210:1+211:1-detail_pvk209:1+210:2+211:1;ch4CateId#*********;ch1CateId#201155301;matchTypeSet:MNR_SEARCH;ctr:0.12739900;cvr:0.00626558;rsc:0.02505168;scr:0.03973964;x2isc:93.82420;msc:93.82419586;text_score:0.64298;reserve_price:50.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:11;localIndex:11;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "956683126772",
                            "triggerItemId": "************",
                            "position": "17",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"6人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n中文版➕英文版\n配套习题分章册工作纸PPT等全套资源\n标价是一本书价格\n需要哪些资料请先沟通",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "6人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "6人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1920,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01fK4KmC1ypDnxkVpFD_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1440
                    },
                    "itemId": "956683126772",
                    "price": "50",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=956683126772",
                    "title": "高中数学新思维mathematics in focus",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i3/O1CN01zkHLhl1ypDn2b1sIb_!!*******************-0-mtopupload.jpg",
                        "userNick": "南宝南宝"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "globalBizCode": "autotrade",
                            "zhimaOffline": "false",
                            "matchType": "QE_SWING_U2I2I",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "13",
                            "id": "951019276055",
                            "guessYouLikeExperimentBucketId": "18",
                            "scm": "1007.12711.395734.0",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1752048382000",
                            "strategyName": "guess_u_like",
                            "item_id": "951019276055",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "15",
                            "ext_info": "recallType:QE_SWING_U2I2I;fromScene:;firstSource:;matchType:QE_SWING_U2I2I;itemId:951019276055;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:934701685319;triggerType:ITEM_RT;rtfeature#detail_pvk211:2-exp_pvk209:2+210:2+211:1;ch4CateId#-1;ch1CateId#0;triggerSet:934701685319%3AITEM_RT;matchTypeSet:QE_SWING_U2I2I;ctr:0.09667733;cvr:0.01020905;rsc:0.02854170;scr:0.03948732;x2isc:596.90405;msc:596.90405273;text_score:0.59645;reserve_price:18.90000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:12;localIndex:12;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "951019276055",
                            "xGlobalBizCode": "idleShop|pinGroup|c2c",
                            "triggerItemId": "************",
                            "position": "15",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"13人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " \n详细可看以下图\n\ndse教辅 dse教材 dse模拟\n\n标价就是全套售价，需要可以直接拍\n\n虚拟产品，售出不退，介意勿拍",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "13人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "13人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1680,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN017bWQOo2DPJdN5MZeZ_!!*******************-53-fleamarket.heic",
                        "widthSize": 856
                    },
                    "itemId": "951019276055",
                    "price": "18.90",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=951019276055",
                    "title": "香港DSE语文复习全套资料",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i2/O1CN01oetJTF2DPJamo2aVY_!!*******************-0-mtopupload.jpg",
                        "userNick": "培优资料库"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "沈阳",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_GMV",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "1",
                            "id": "947366429890",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1751385026000",
                            "strategyName": "guess_u_like",
                            "item_id": "947366429890",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "20",
                            "ext_info": "recallType:SWING_I2I_GMV;fromScene:;firstSource:;matchType:SWING_I2I_GMV;itemId:947366429890;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#-1;ch1CateId#0;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_GMV;ctr:0.19832274;cvr:0.00460961;rsc:0.03078770;scr:0.03944633;x2isc:0.08264;msc:0.08264000;text_score:0.82467;reserve_price:12.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:13;localIndex:13;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "947366429890",
                            "triggerItemId": "************",
                            "position": "20",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"1人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "1-6级 英语课件  学生书、答案、音频、教学课件PPT、测试含答案（期中+期末）、单词表、听力文稿、课程计划等  《Listen Up, Speak Out》是一套注重英语听说能力培养的实用教材，专为亚洲学习者设计。教材通过真实生活场景对...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "1人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "1人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1500,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN01tsAmLG2MCKww50gBl_!!4611686018427386063-0-fleamarket.jpg",
                        "widthSize": 1500
                    },
                    "itemId": "947366429890",
                    "price": "12",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=947366429890",
                    "title": "英语听说教材 Listen Up, Speak Out 全套",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01enXgqE2MCKrMcNbnj_!!0-mtopupload.jpg",
                        "userNick": "白菜炖豆腐"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "globalBizCode": "autotrade",
                            "zhimaOffline": "false",
                            "matchType": "SWING_U2I2I_pay",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "1",
                            "id": "950449725452",
                            "guessYouLikeExperimentBucketId": "18",
                            "scm": "1007.12711.395734.0",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1752046457000",
                            "strategyName": "guess_u_like",
                            "item_id": "950449725452",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "16",
                            "ext_info": "recallType:SWING_U2I2I_pay;fromScene:;firstSource:;matchType:SWING_U2I2I_pay;itemId:950449725452;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:950449725452;triggerType:ITEM_RT;rtfeature#exp_pvk211:1-detail_pvk210:1;ch4CateId#-1;ch1CateId#0;triggerSet:950449725452%3AITEM_RT;matchTypeSet:SWING_U2I2I_pay;ctr:0.16286755;cvr:0.00424805;rsc:0.02381145;scr:0.03821886;x2isc:1.00000;msc:1.00000000;text_score:0.65811;reserve_price:55.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:14;localIndex:14;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "950449725452",
                            "xGlobalBizCode": "idleShop|pinGroup|c2c",
                            "triggerItemId": "************",
                            "position": "16",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"1人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "rd第三版全套 拍下发全套，需要可以直接拍 Text book，教学PPT、模拟试卷、课堂练习、模拟练习、听力等各类教师资源等  电子资料售出不退换介意勿拍",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "1人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "1人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1288,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN017YyZHN2DPJdMm6Nod_!!*******************-53-fleamarket.heic",
                        "widthSize": 1264
                    },
                    "itemId": "950449725452",
                    "price": "55",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=950449725452",
                    "title": "DSE初中英语，牛津Oxford English Forwa",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i2/O1CN01oetJTF2DPJamo2aVY_!!*******************-0-mtopupload.jpg",
                        "userNick": "培优资料库"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "惠州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "MNR_HOME",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "37",
                            "id": "814466925132",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1720568913000",
                            "strategyName": "guess_u_like",
                            "item_id": "814466925132",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "23",
                            "ext_info": "recallType:MNR_HOME;fromScene:;firstSource:;matchType:MNR_HOME;itemId:814466925132;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:-1;triggerType:-1;rtfeature#exp_pvk209:1;ch4CateId#201148605;ch1CateId#201155301;matchTypeSet:MNR_HOME;ctr:0.11794403;cvr:0.00473678;rsc:0.01868464;scr:0.03788365;x2isc:66.62415;msc:66.62414551;text_score:0.64910;reserve_price:60.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:15;localIndex:15;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "814466925132",
                            "triggerItemId": "************",
                            "position": "23",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"37人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\nLongman English World 朗文英语世界 3B正版主课本教材培生 学生用书\n这种教材都很贵，自己可以查一下网上都是1百多一本 现清货，便宜出 有二维码可以扫码 ，全新正版",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "37人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "37人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1108,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01KkZ5dW1sMT9QZIeZp_!!0-fleamarket.jpg",
                        "widthSize": 1077
                    },
                    "itemId": "814466925132",
                    "price": "60",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=814466925132",
                    "title": "朗文教材全新主教材以及综合练习册",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "山脉旁高兴过头的海星"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "REDIRECT_COLLECT",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "26",
                            "id": "759505241954",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1703995957000",
                            "strategyName": "guess_u_like",
                            "item_id": "759505241954",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "7",
                            "ext_info": "recallType:REDIRECT_COLLECT;fromScene:;firstSource:;matchType:REDIRECT_COLLECT;itemId:759505241954;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:759505241954;triggerType:XY_ITEM_COLLECT;rtfeature#detail_pvk211:1;ch4CateId#*********;ch1CateId#201155301;triggerSet:759505241954%3AXY_ITEM_COLLECT;matchTypeSet:REDIRECT_COLLECT;ctr:0.16406938;cvr:0.00498232;rsc:0.02699894;scr:0.03768446;x2isc:4.20200;msc:4.20200014;text_score:0.56138;reserve_price:19.99000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:16;localIndex:16;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "759505241954",
                            "triggerItemId": "************",
                            "position": "7",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_41\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"¥999\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"26人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n感兴趣的话点“我想要”和我私聊吧～",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "41",
                                        "isStrikeThrough": "true",
                                        "lineHeight": "1.0",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "¥999",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "¥999"
                                        },
                                        "arg1": "99_tag_r3_41"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "26人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "26人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1042,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01TsiQcx2LwlKGFyOEC_!!0-fleamarket.jpg",
                        "widthSize": 1152
                    },
                    "itemId": "759505241954",
                    "price": "19.99",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=759505241954",
                    "title": "DSE语文文言文20篇原文+解析",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i1/6000000004983/O1CN01jVBU1Z1mgGCz8w3FI_!!6000000004983-0-userheaderimgshow.jpg",
                        "userNick": "DSEr"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "广州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "20",
                            "id": "899138290422",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1742118732000",
                            "strategyName": "guess_u_like",
                            "item_id": "899138290422",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "25",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:899138290422;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#exp_pvk209:1;ch4CateId#*********;ch1CateId#201155301;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.09996694;cvr:0.02088422;rsc:0.05619196;scr:0.03742630;x2isc:1.09500;msc:1.09500003;text_score:0.83158;reserve_price:1.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:17;localIndex:17;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "899138290422",
                            "triggerItemId": "************",
                            "position": "25",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"20人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " 高清彩色页面 \nListen up plus\n\n✅非纸质，电子版资源，PDF，宝贝详情有截图实拍 资源目录\n[红圆]直接拍下发以下全部资源，资料具体包含：  \n\nListen up 1 2 3 课本+练习册+音频+答案+词汇表\nListen up PLUS 1 2 3 课本+练习册+答案+TEST+词汇表+听力文本\n[红圆]PLUS 1 2无 练习册，无音频，介意不拍\n\n稀缺资源！家长们最期待的少儿英语听力训练教材《Listen up》电子版来了，教材pdf+音频+练习册，一套齐全。\n专为5-11岁的孩子设计！\n\nListen up 初阶共3册,适用5-8岁；\nListen up plus加强版共3册,适用8-11岁。plus版音频不全介意勿拍\n\n[五角星]推荐理由：\n贴近生活——学习内容与日常生活息息相关；生动幽默——趣味卡通形象提升孩子的兴趣点；稳扎稳打——词汇练习句型练习两不误；温故知新——复习单元帮你牢牢记住所学知识；\n \n[五角星]这套教材每个阶段又分3册，遵循先听后说的学习规律，通过环环紧扣的科学训练将词汇练习与句型练习完美结合，让孩子感受纯英文的学习环境和生活场景，通过多样的题型设置，如填写字母、听词圈词、补全句子，看图写话等多样的练习形式迅速提升听力技能。\n\n[五角星]只要能认真学习完，你会发现你的孩子已经收获了以下效果：\n1. 听懂小学阶段的重点词汇1000个左右，掌握英语句型200个左右；\n\n2. 形成对英语时态的初级认知，如一般现在时、一般过去时、一般将来时、现在进行时、现在完成时、过去完成时等；\n\n3. 听懂近百个日常主题的小对话。  \n\n▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼\n【购买须知】\n✅ 无客服在线，自助下单，直接拍，拍下百度网盘自动秒发！\n✅ 电子版资源，详情里有资料说明，有实拍图，有写的都有，没写的没有。\n✅ 电子资料具有可复制性，一旦发货，不退款，感谢理解 ！",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "20人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "20人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 738,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN01JytWNS24lubMYSxKK_!!*******************-0-fleamarket.jpg",
                        "widthSize": 828
                    },
                    "itemId": "899138290422",
                    "price": "1",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=899138290422",
                    "title": "Listen up 少儿英语听力专项训练教材 电子版pdf",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01xBVEt324lubfJaVz5_!!*******************-0-mtopupload.jpg",
                        "userNick": "拍下自动发货"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_GMV",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "19",
                            "id": "913966714720",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1745195964000",
                            "strategyName": "guess_u_like",
                            "item_id": "913966714720",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "12",
                            "ext_info": "recallType:SWING_I2I_GMV;fromScene:;firstSource:;matchType:SWING_I2I_GMV;itemId:913966714720;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#*********;ch1CateId#201155301;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_GMV;ctr:0.11374348;cvr:0.01133174;rsc:0.03677252;scr:0.03729754;x2isc:0.09078;msc:0.09077800;text_score:0.61568;reserve_price:3.99000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:18;localIndex:18;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "913966714720",
                            "triggerItemId": "************",
                            "position": "12",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r2_824\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"回头客超85%卖家\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"19人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n电子资料 标价为一本的价格，网盘发货发货后不退不换.",
                    "fishTags": {
                        "r2": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#FF7900",
                                        "size": "12",
                                        "labelId": "824",
                                        "lineHeight": "1.5",
                                        "type": "text",
                                        "content": "回头客超85%卖家"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "回头客超85%卖家"
                                        },
                                        "arg1": "99_tag_r2_824"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "true"
                            }
                        },
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "19人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "19人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1318,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN01X5lxQB1DGbuFvfPPt_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1507
                    },
                    "itemId": "913966714720",
                    "price": "3.99",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=913966714720",
                    "title": "英国初中历史 Oxford KS3 History 4th",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01ycvXbf1DGbu2GeDZE_!!*******************-0-mtopupload.jpg",
                        "userNick": "苏苏林吉特"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "广州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "18",
                            "id": "882464948338",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1737887319000",
                            "strategyName": "guess_u_like",
                            "item_id": "882464948338",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "27",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:882464948338;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#exp_pvk211:1;ch4CateId#126864808;ch1CateId#126867000;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.07918653;cvr:0.01883316;rsc:0.04045078;scr:0.03693810;x2isc:0.12880;msc:0.12880000;text_score:0.66752;reserve_price:2.68000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:19;localIndex:19;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "882464948338",
                            "triggerItemId": "************",
                            "position": "27",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"18人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "ding With Writing +Listening and Speaking《阅读和写作+听力和口语》 1-6级共12册\n\n自动秒发，电紫资料，非纸质，佰度及夸克双端网盘发货！\n\n[红旗]发货内容：\n《牛津技能世界：阅读和写作+听力和口语》1-6级共12册\n1-6级学生书+音频+答案\n1-6级教师书+课堂音频\n1-6级单词表+答案+阅读测试+词汇表+写作指导+课程设计教师资源包\n\n[灯泡]这是一套口碑极好的小学英语阅读写作教材，让孩子掌握多种思维工具和考试技巧，对接剑桥、少儿托福等国际主流考试。教材：小学英语读写专项教材适合对象：小学生，6-12岁级别册数：1-6，6级别6册每册单元数：6大主题，12个单元CEFR等级：A1-B1\n\n[灯泡]直接拍，发bai度及夸克网盘，电紫资料拍下不退不换，看清楚再拍。\n\n[灯泡]本小店所有物品，均通过网络等合法渠道获得，用于阅读交流使用，商品价格为网盘存储及人工整理费用，非资料价格，如有侵权，望告知，将立即删除。",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "18人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "18人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1192,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01arACqb2H7gqEqvzac_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1153
                    },
                    "itemId": "882464948338",
                    "price": "2.68",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=882464948338",
                    "title": "牛ji技能世界Oxford Skills World Rea",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i1/O1CN016PNq3Q2H7go9tL05d_!!*******************-0-mtopupload.jpg",
                        "userNick": "中小学电子学习资料库"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "8",
                            "id": "940886328379",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1750007161000",
                            "strategyName": "guess_u_like",
                            "item_id": "940886328379",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "6",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:940886328379;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#*********;ch1CateId#201155301;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.17719296;cvr:0.00379488;rsc:0.02389836;scr:0.03684292;x2isc:3.42610;msc:3.42610002;text_score:0.72610;reserve_price:40.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:20;localIndex:20;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "940886328379",
                            "triggerItemId": "************",
                            "position": "6",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_41\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"¥40.1\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"8人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " 【全新包邮】少儿英语 Building Skills for Listening 1-3级听力专项训练彩色喷墨打印定制款学习  全新包邮，彩色喷墨打印款，高清，定制款，介意慎拍[红圆]  [红圆]包含： Discovering1-3级 B...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "41",
                                        "isStrikeThrough": "true",
                                        "lineHeight": "1.0",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "¥40.1",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "¥40.1"
                                        },
                                        "arg1": "99_tag_r3_41"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "8人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "8人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 800,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01EYr5Gr1ro7Wvcbwmu_!!4611686018427385309-53-fleamarket.heic",
                        "widthSize": 800
                    },
                    "itemId": "940886328379",
                    "price": "40",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=940886328379",
                    "title": "【全新包邮】少儿英语 Building Skills for",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "家有学霸"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "武汉",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "77",
                            "id": "847714992670",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1729931540000",
                            "strategyName": "guess_u_like",
                            "item_id": "847714992670",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "9",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:847714992670;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#exp_pvk211:1;ch4CateId#127832004;ch1CateId#127818002;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.11325219;cvr:0.00695759;rsc:0.02422915;scr:0.03588540;x2isc:0.11690;msc:0.11690000;text_score:0.88337;reserve_price:29.80000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:21;localIndex:21;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "847714992670",
                            "triggerItemId": "************",
                            "position": "9",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"77人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_662\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"sevenIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "uture Starter Dream 1/2/3/4送配套音频  高清彩色印刷，质量很棒，附图为实拍图~ 全套有有starter、dream、discover3个大级别，每个大级别包含3个小级别。 4级咖啡色也有   [质量与有货] 1：...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "77人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "77人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "662",
                                        "width": "72",
                                        "type": "img",
                                        "content": "sevenIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01k7P6QR1DbfqYrsY5N_!!6000000000235-2-tps-216-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "sevenIcon"
                                        },
                                        "arg1": "99_tag_r5_662"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1268,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN013bBJVv1md3pGqeS68_!!53-fleamarket.heic",
                        "widthSize": 954
                    },
                    "itemId": "847714992670",
                    "price": "29.80",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=847714992670",
                    "title": "全新Compass 21世纪跨学科少儿英语Reading F",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01sv4NQr1md3VqfSS0o_!!0-mtopupload.jpg",
                        "userNick": "juan"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "XINPIN_SRCH_COEXPO_U2I2I_HOUR",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "1",
                            "id": "960790456400",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1753938145000",
                            "strategyName": "guess_u_like",
                            "item_id": "960790456400",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "13",
                            "ext_info": "recallType:XINPIN_SRCH_COEXPO_U2I2I_HOUR;fromScene:;firstSource:;matchType:XINPIN_SRCH_COEXPO_U2I2I_HOUR;itemId:960790456400;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:945308003189;triggerType:ITEM_LT;rtfeature#exp_pvk209:2+210:1+211:1-detail_pvk210:1;ch4CateId#127812005;ch1CateId#127818002;triggerSet:945308003189%3AITEM_LT;matchTypeSet:XINPIN_SRCH_COEXPO_U2I2I_HOUR;ctr:0.08832094;cvr:0.00614506;rsc:0.01710127;scr:0.03584930;x2isc:0.50000;msc:0.50000000;text_score:0.72342;reserve_price:80.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:22;localIndex:22;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "960790456400",
                            "triggerItemId": "************",
                            "position": "13",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r2_521\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"48小时内发布\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"1人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "\n可刀！态度好可以来问\n感兴趣的话点“我想要”和我私聊吧～",
                    "fishTags": {
                        "r2": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#3B3F49",
                                        "gradientDirection": "to right",
                                        "type": "gradientImageText",
                                        "content": "48小时内发布",
                                        "leftImage": {
                                            "marginRight": "2",
                                            "width": "14",
                                            "url": "https://gw.alicdn.com/imgextra/i3/O1CN01Z6BeDa1w4qA2xZLFJ_!!6000000006255-2-tps-42-42.png",
                                            "height": "14",
                                            "marginLeft": "2"
                                        },
                                        "rightImage": {},
                                        "gradientColors": [
                                            "#F7F7CC",
                                            "#FFFFFF"
                                        ],
                                        "borderRadius": "9",
                                        "size": "12",
                                        "labelId": "521",
                                        "lineHeight": "1.2",
                                        "gradientType": "linear",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "48小时内发布"
                                        },
                                        "arg1": "99_tag_r2_521"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "true"
                            }
                        },
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "1人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "1人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1920,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN015Ktfdr1IVKkHavqcm_!!4611686018427381586-0-fleamarket.jpg",
                        "widthSize": 1440
                    },
                    "itemId": "960790456400",
                    "price": "80",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=960790456400",
                    "title": "出香港中四教材Journey Through History",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "凌晨四点海棠花"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "上海",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "REDIRECT_CHAT",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "5",
                            "id": "890569535954",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1740128019000",
                            "strategyName": "guess_u_like",
                            "item_id": "890569535954",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "2",
                            "ext_info": "recallType:REDIRECT_CHAT;fromScene:;firstSource:;matchType:REDIRECT_CHAT;itemId:890569535954;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:890569535954;triggerType:XY_ITEM_CHAT;rtfeature#null;ch4CateId#*********;ch1CateId#201155301;triggerSet:890569535954%3AXY_ITEM_CHAT;matchTypeSet:REDIRECT_CHAT;ctr:0.18049145;cvr:0.00494370;rsc:0.02952704;scr:0.03575002;x2isc:5.21400;msc:5.21400023;text_score:0.46313;reserve_price:8.88000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:23;localIndex:23;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "890569535954",
                            "triggerItemId": "************",
                            "position": "2",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r2_824\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"回头客超85%卖家\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"5人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": " 具体需要可咨询，标价为单一版本全套。 需要单个年级可咨询，有问必答。 电子资料售出不退不换。 #资料",
                    "fishTags": {
                        "r2": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#FF7900",
                                        "size": "12",
                                        "labelId": "824",
                                        "lineHeight": "1.5",
                                        "type": "text",
                                        "content": "回头客超85%卖家"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "回头客超85%卖家"
                                        },
                                        "arg1": "99_tag_r2_824"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "true"
                            }
                        },
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "5人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "5人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1016,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01ueNAsB1jAiV1AZFg3_!!*******************-53-fleamarket.heic",
                        "widthSize": 1004
                    },
                    "itemId": "890569535954",
                    "price": "8.88",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=890569535954",
                    "title": "dse牛津新世代数学教材中英两版",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i3/O1CN01JzxKVc1jAiWUL8Y7b_!!*******************-0-mtopupload.jpg",
                        "userNick": "唐朝幸运的三七"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "11",
                            "id": "926745453516",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1747707604000",
                            "strategyName": "guess_u_like",
                            "item_id": "926745453516",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "3",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:926745453516;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#-1;ch1CateId#0;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.18501270;cvr:0.00441656;rsc:0.02782848;scr:0.03565487;x2isc:0.40220;msc:0.40220001;text_score:0.87490;reserve_price:12.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:24;localIndex:24;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "926745453516",
                            "triggerItemId": "************",
                            "position": "3",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"11人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "1-6级全套高清资料含课本课件PPT测试音频等  资料详情: 高清学生电孑版 课本PPT 答案 测试(期中期末) 单词表 词汇测 教师规划 音频  低龄学习者：该系列面向 7-12 岁的儿童，适合刚开始英语学习之旅的学生。 初级到高级初级水...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "11人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "11人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 864,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN01KG9ORj2MbWimAsye2_!!4611686018427384566-0-fleamarket.jpg",
                        "widthSize": 1248
                    },
                    "itemId": "926745453516",
                    "price": "12",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=926745453516",
                    "title": "compass听说教材listen up speak out",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "学习很快乐"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "北京",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_GMV",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "12",
                            "id": "947832640055",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1751367867000",
                            "strategyName": "guess_u_like",
                            "item_id": "947832640055",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "4",
                            "ext_info": "recallType:SWING_I2I_GMV;fromScene:;firstSource:;matchType:SWING_I2I_GMV;itemId:947832640055;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#detail_pvk209:1+211:1-exp_pvk209:1+210:2+211:1-clk_pvk209:1-chat_pvk209:1-pay_pvk209:1;ch4CateId#201459411;ch1CateId#201451101;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_GMV;ctr:0.12081859;cvr:0.00614506;rsc:0.02339368;scr:0.03542773;x2isc:0.14747;msc:0.14746600;text_score:0.79956;reserve_price:35.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:25;localIndex:25;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "947832640055",
                            "triggerItemId": "************",
                            "position": "4",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_598\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"累计降价49%\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"12人想要\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "st! Reading with Phonics and Speaking 1-6 全6级少儿英语分级阅读  [五角星]百度网盘自动发货，发货后不退不换  [火]全套共6级别，每个级别分3个阶段共18册学生书高清PDF，送全套教师资源含课件...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#FF4400",
                                        "size": "12",
                                        "labelId": "598",
                                        "lineHeight": "1.3",
                                        "type": "gradientImageText",
                                        "content": "累计降价49%",
                                        "leftImage": {
                                            "marginRight": "3",
                                            "width": "6",
                                            "url": "https://gw.alicdn.com/imgextra/i4/O1CN01jkcmvf1jktWeur5JU_!!6000000004587-2-tps-18-33.png",
                                            "height": "11"
                                        }
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "累计降价49%"
                                        },
                                        "arg1": "99_tag_r3_598"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "12人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "12人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1280,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN01YhFZiU1LKI2AfZVKV_!!4611686018427381216-53-fleamarket.heic",
                        "widthSize": 1280
                    },
                    "itemId": "947832640055",
                    "price": "35",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=947832640055",
                    "title": "秒发送课件PPT全套价The Best阅读系列 The Be",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "DDEW"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "北京",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_GMV",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "128",
                            "id": "776531180116",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1711176125000",
                            "strategyName": "guess_u_like",
                            "item_id": "776531180116",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "28",
                            "ext_info": "recallType:SWING_I2I_GMV;fromScene:;firstSource:;matchType:SWING_I2I_GMV;itemId:776531180116;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#127812005;ch1CateId#127818002;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_GMV;ctr:0.13002735;cvr:0.00555497;rsc:0.02325856;scr:0.03534425;x2isc:0.14854;msc:0.14854300;text_score:0.80279;reserve_price:35.90000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:26;localIndex:26;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "776531180116",
                            "triggerItemId": "************",
                            "position": "28",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_1017\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"可小刀\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"128人想要\"}},{\"arg1\":\"99_tag_r4_468\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"你关注过的人\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "语听力训练教材  采用简单、有趣的听力材料，逐一拆解听英语听力的难点，带孩子从听人名、听数字开始，到听时态、听状态&hellip;&hellip;循序渐进，逐个击破！  在国内，像《Listen Up/Plus》这样专业的、针对小学零基础弱...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "labelId": "1017",
                                        "width": "50",
                                        "type": "img",
                                        "content": "可小刀",
                                        "url": "https://gw.alicdn.com/imgextra/i2/O1CN01fttvvl1LBaPWf4uuH_!!6000000001261-2-tps-150-48.png",
                                        "height": "16",
                                        "marginLeft": "2"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "可小刀"
                                        },
                                        "arg1": "99_tag_r3_1017"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "128人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "128人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "468",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "你关注过的人",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "你关注过的人"
                                        },
                                        "arg1": "99_tag_r4_468"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1040,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01t4DvN81daSyjBf2QI_!!0-fleamarket.jpg",
                        "widthSize": 1040
                    },
                    "itemId": "776531180116",
                    "price": "35.90",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=776531180116",
                    "title": "全新麦克森专项Listen up/Plus 1/2/3少儿英",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i1/O1CN01wsdbvk1daSwcp9CdD_!!0-mtopupload.jpg",
                        "userNick": "好宝贝英语资料站"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "重庆",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "40",
                            "id": "941996468921",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1750210998000",
                            "strategyName": "guess_u_like",
                            "item_id": "941996468921",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "29",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:941996468921;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#exp_pvk209:2+210:2+211:1;ch4CateId#-1;ch1CateId#0;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.10120380;cvr:0.01912400;rsc:0.05243369;scr:0.03492313;x2isc:0.18730;msc:0.18730000;text_score:0.64192;reserve_price:1.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:27;localIndex:27;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "941996468921",
                            "triggerItemId": "************",
                            "position": "29",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_598\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"累计降价91%\"}},{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"40人想要\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "pt，送全|||套电子版教材及教学资源，含最齐全学生书答案和练习册答案，老师上课可以直接使用\n\n[右]标价是全套价！\n[右]电子版百度网盘发，发货后不退不换\n\n[火]全九套课件具体内容包括:\n1 Writing Framework for Paragraph 1\n2 Writing Framework for Paragraph 2\n3 Writing Framework for Paragraph 3\n4 Writing Framework for Sentence 1\n5 Writing Framework for Sentence 2\n6 Writing Framework for Sentence 3\n7 Writing Framework for Essay 1\n8 Writing Framework for Essay 2\n9 Writing Framework for Essay 3\n\n[火]送全套高清教材PDF及教学资源\n音频，测试，教师书，词汇表，教案，教学大纲等\n[火]最齐全答案:全套学生书+练习册+学生书答案+练习册答案",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#FF4400",
                                        "size": "12",
                                        "labelId": "598",
                                        "lineHeight": "1.3",
                                        "type": "gradientImageText",
                                        "content": "累计降价91%",
                                        "leftImage": {
                                            "marginRight": "3",
                                            "width": "6",
                                            "url": "https://gw.alicdn.com/imgextra/i4/O1CN01jkcmvf1jktWeur5JU_!!6000000004587-2-tps-18-33.png",
                                            "height": "11"
                                        }
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "累计降价91%"
                                        },
                                        "arg1": "99_tag_r3_598"
                                    }
                                },
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "40人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "40人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 860,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01RSpIUc1xEkdaCbj1U_!!4611686018427380044-0-fleamarket.jpg",
                        "widthSize": 848
                    },
                    "itemId": "941996468921",
                    "price": "1",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=941996468921",
                    "title": "【全套价】Writing Framework 写作框架课件p",
                    "user": {
                        "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                        "userNick": "呵呵哒哒"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "深圳",
                    "clickParam": {
                        "args": {
                            "globalBizCode": "autotrade",
                            "zhimaOffline": "false",
                            "matchType": "REDIRECT_COLLECT",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "7",
                            "id": "953027139845",
                            "guessYouLikeExperimentBucketId": "18",
                            "scm": "1007.12711.395734.0",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1752827656000",
                            "strategyName": "guess_u_like",
                            "item_id": "953027139845",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "0",
                            "ext_info": "recallType:REDIRECT_COLLECT;fromScene:;firstSource:;matchType:REDIRECT_COLLECT;itemId:953027139845;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:953027139845;triggerType:XY_ITEM_COLLECT;rtfeature#exp_pvk209:1+210:1+211:1;ch4CateId#*********;ch1CateId#201155301;triggerSet:953027139845%3AXY_ITEM_COLLECT;matchTypeSet:REDIRECT_COLLECT;ctr:0.08966690;cvr:0.02161533;rsc:0.05204116;scr:0.03466169;x2isc:4.16300;msc:4.16300011;text_score:0.69995;reserve_price:1.00000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:28;localIndex:28;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "953027139845",
                            "xGlobalBizCode": "welfare|welfareDonation|c2c",
                            "triggerItemId": "************",
                            "position": "0",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"7人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用优秀\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "试香港各科目2025插班生考试试卷\n\n[火]资料内容：\n各个名校的期中期末试卷，各科都有。\n中一36套\n中二34套\n中三71套\n中四34套\n\n#助力学习好物 \n\n[送花]资料可复制，保存后不退不换，介意勿拍！",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "7人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "7人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用优秀",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用优秀"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 1920,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01o4jccf1ZKCaF7t9sx_!!*******************-53-fleamarket.heic",
                        "widthSize": 872
                    },
                    "itemId": "953027139845",
                    "price": "1",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=953027139845",
                    "title": "香港中一二三四初中呈分式名校试卷练习band1中学插班入学考",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01gbHSgW1ZKCa2SUmWd_!!*******************-0-mtopupload.jpg",
                        "userNick": "小羊苏茜"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            },
            {
                "cardData": {
                    "area": "广州",
                    "clickParam": {
                        "args": {
                            "zhimaOffline": "false",
                            "matchType": "SWING_I2I_DAY",
                            "userHashFlag": "34",
                            "type": "1",
                            "bucket_id": "34",
                            "pvid": "5720d7cc-ed70-4b9f-adaf-a7a2505a9c57",
                            "wantNum": "7",
                            "id": "924332121092",
                            "scm": "1007.12711.395734.0",
                            "guessYouLikeExperimentBucketId": "18",
                            "rn_xy": "215042e817541092331414024e1572",
                            "recItemNum": "80",
                            "publishTime": "1747310660000",
                            "strategyName": "guess_u_like",
                            "item_id": "924332121092",
                            "oldZhima": "false",
                            "unShowLabelParams": "{}",
                            "cardType": "default",
                            "index": "21",
                            "ext_info": "recallType:SWING_I2I_DAY;fromScene:;firstSource:;matchType:SWING_I2I_DAY;itemId:924332121092;mainItem:************;mainSeller:0;query:<UNK>;triggerKey:************;triggerType:MAIN_ITEM;rtfeature#null;ch4CateId#*********;ch1CateId#201155301;triggerSet:************%3AMAIN_ITEM;matchTypeSet:SWING_I2I_DAY;ctr:0.11532810;cvr:0.01133174;rsc:0.03728482;scr:0.03404706;x2isc:0.30980;msc:0.30980000;text_score:0.83126;reserve_price:2.68000;isEmbNull:0;enable_chat:0.0;prerank_modelscore:0;prerank_ctrscore:0;prerank_cvrscore:0;tpp_trace:215042e817541092331414024e1572;mount_expose:true;idle_mount_tai_task_abs:1904%3AT%3A0%3B0%3AT%3A1%3B2060%3AT%3A0%3B2140%3AT%3A0%3B2128%3AC%3A0%3B1540%3AT%3A0%3B2102%3AC%3A0%3B1911%3AT%3A0%3B1539%3AT%3A0%3B2044%3AT%3A0%3B485%3AT%3A0%3B-10%3AC%3A1%3B-4%3AC%3A1%3B1231%3AT%3A0%3B486%3AT%3A0%3B2155%3AT%3A0%3B787%3AT%3A0%3B1227%3AT%3A0%3B838%3AT%3A0%3B598%3AT%3A0%3B935%3AT%3A0%3B195%3AT%3A0%3B2005%3AT%3A0%3B1146%3AT%3A0%3B365%3AT%3A0%3B2129%3AT%3A0%3B1103%3AT%3A0%3B1198%3AT%3A0%3B2208%3AT%3A0%3B2233%3AT%3A0%3B1933%3AT%3A0%3B1256%3AC%3A0%3B226%3AT%3A0%3B519%3AC%3A0%3B243%3AT%3A0%3B1912%3AC%3A0%3B2065%3AT%3A0%3B464%3AT%3A0%3B141%3AT%3A0%3B1949%3AT%3A0%3B969%3AT%3A0%3B227%3AT%3A0%3B2068%3AT%3A0%3B2038%3AT%3A0%3B465%3AT%3A0%3B1537%3AT%3A0%3B1948%3AT%3A0%3B516%3AT%3A0%3B550%3AT%3A0%3B2192%3AC%3A0%3B2182%3AT%3A0;index:29;localIndex:29;hbuck:2711%230%23395734%230_2711%2326939%23487162%2345_2711%2326859%23511910%2337_2711%2326937%23485386%2319",
                            "strategyIndex": "0",
                            "itemId": "924332121092",
                            "triggerItemId": "************",
                            "position": "21",
                            "rn": "7f50530280ac7caeba449da31ae03fd1",
                            "serviceUtParams": "[{\"arg1\":\"99_tag_r3_9\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"7人想要\"}},{\"arg1\":\"99_tag_r4_919\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"卖家信用极好\"}},{\"arg1\":\"99_tag_r5_731\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"24hsIcon\"}},{\"arg1\":\"99_tag_r5_664\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"nfrIcon\"}},{\"arg1\":\"99_tag_r1_13\",\"args\":{\"LabelSystem2.0\":\"1\",\"content\":\"freeShippingIcon\"}}]",
                            "labelBucketId": "18",
                            "zhimaLogBucketId": "4"
                        },
                        "spm": "a21ybx.item",
                        "arg1": "Item"
                    },
                    "desc": "  听力教材 E-future listen up listenup plus   ✅非纸质，电子版资源，PDF，下拉有截图实拍 资源目录 [红圆]拍下发以下全部资源，资料具体包含：  listen up 1 2 3 课本+练习册+音频+答...",
                    "fishTags": {
                        "r3": {
                            "tagList": [
                                {
                                    "data": {
                                        "color": "#999999",
                                        "size": "12",
                                        "labelId": "9",
                                        "lineHeight": "1.5",
                                        "bold": "false",
                                        "type": "text",
                                        "content": "7人想要",
                                        "marginLeft": "4"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "7人想要"
                                        },
                                        "arg1": "99_tag_r3_9"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r4": {
                            "tagList": [
                                {
                                    "data": {
                                        "bgColor": "#FFF4EB",
                                        "color": "#FF7900",
                                        "borderRadius": "8",
                                        "size": "11",
                                        "labelId": "919",
                                        "borderPaddingLeft": "6",
                                        "lineHeight": "1.3",
                                        "borderPaddingRight": "6",
                                        "type": "gradientImageText",
                                        "content": "卖家信用极好",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "卖家信用极好"
                                        },
                                        "arg1": "99_tag_r4_919"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r5": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "731",
                                        "width": "59",
                                        "type": "img",
                                        "content": "24hsIcon",
                                        "url": "https://img.alicdn.com/imgextra/i1/O1CN01tFonKm1bdYHeExRB5_!!6000000003488-2-tps-174-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "24hsIcon"
                                        },
                                        "arg1": "99_tag_r5_731"
                                    }
                                },
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "664",
                                        "width": "76",
                                        "type": "img",
                                        "content": "nfrIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i1/O1CN01IdADAH1ZMwenlz92N_!!6000000003181-2-tps-228-42.png",
                                        "height": "14"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "nfrIcon"
                                        },
                                        "arg1": "99_tag_r5_664"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        },
                        "r1": {
                            "tagList": [
                                {
                                    "data": {
                                        "marginRight": "4",
                                        "labelId": "13",
                                        "width": "28",
                                        "type": "img",
                                        "alignment": "middle",
                                        "content": "freeShippingIcon",
                                        "url": "https://gw.alicdn.com/imgextra/i3/O1CN01PuymOm1I7yIlVyFV9_!!6000000000847-2-tps-84-48.png",
                                        "height": "16"
                                    },
                                    "utParams": {
                                        "args": {
                                            "LabelSystem2.0": "1",
                                            "content": "freeShippingIcon"
                                        },
                                        "arg1": "99_tag_r1_13"
                                    }
                                }
                            ],
                            "config": {
                                "mutualLabelBizGroup": "false"
                            }
                        }
                    },
                    "image": {
                        "heightSize": 879,
                        "major": true,
                        "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01Dxh9dL24lucIchIDM_!!*******************-0-fleamarket.jpg",
                        "widthSize": 1334
                    },
                    "itemId": "924332121092",
                    "price": "2.68",
                    "showVideoIcon": "false",
                    "targetUrl": "https://www.goofish.com/item?id=924332121092",
                    "title": "Listen up+Listen up plus 1 2 3",
                    "user": {
                        "avatar": "http://img.alicdn.com/bao/uploaded/i4/O1CN01xBVEt324lubfJaVz5_!!*******************-0-mtopupload.jpg",
                        "userNick": "拍下自动发货"
                    }
                },
                "cardType": 100005,
                "idleAdsTaskId": "null",
                "mocked": false,
                "moreTopicId": "null"
            }
        ],
        "hasMore": true,
        "needDecryptKeys": []
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}



### 调用接口2

#### 请求参数

https://h5api.m.goofish.com/h5/mtop.taobao.idle.pc.detail/1.0/?jsv=2.7.2&appKey=********&t=*************&sign=d611ab023a37e562c06ba8ea8b5d58ff&v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.taobao.idle.pc.detail&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.item.0.0

#### 响应参数

{
    "api": "mtop.taobao.idle.pc.detail",
    "data": {
        "b2cBuyerDO": {
            "buyQualificationActList": [],
            "isFirstOrderUser": false,
            "isNewUserIn7Day": false,
            "favored": false
        },
        "itemDO": {
            "noPicItem": false,
            "shareData": {
                "shareReportUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/cro-report-center/pages/report-type?titleVisible=false&useCusFont=true&pageSourceCode=itemDetail&reportSubjectId=************",
                "shareHelpUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/customer-service-hall/pages/home?loadingVisible=false&fromType=ITEM_DETAIL&from=kVNljbMN5B&item_id=************",
                "shareInfoJsonString": "{\"canShareToWxMini\":true,\"contentParams\":{\"headerParams\":{\"subTitle\":\"北京\",\"title\":\"剑桥考伴\",\"userAvatar\":\"http://img.alicdn.com/bao/uploaded/i4/O1CN01pRXocR1Mhw5MDCzBt_!!*******************-0-mtopupload.jpg\"},\"mainParams\":{\"content\":\"Compass少儿英语听力教材 Discovering/Activating/Expanding/Building Skills for Listening全套听力训练高清全套惊喜价更划算\\n\\n[1]Discovering Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿➕答案  A\\n\\n[2]Activating Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿（无答案）08\\n\\n[3]Expanding Skills for Listening 1,2,3级别学生用书pdf+原版音频+听力文稿➕答案   01\\n\\n4️⃣Building Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿➕答案\",\"contentMaxLine\":\"2\",\"extra\":{\"descRelativeTags\":[],\"priceRelativeTags\":[{\"additionMap\":{},\"bgColor\":\"#FFFFFF\",\"borderColor\":\"#FFFFFF\",\"order\":99,\"text\":\"包邮\",\"textColor\":\"#1F1F1F\",\"trackParams\":{\"tagType\":\"baoyou\"}}],\"soldPrice\":\"1\"},\"images\":[{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i3/O1CN01F8jlJc1Mhw5qVtAU6_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i4/O1CN01v0sJD01Mhw5rGkYMn_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i1/O1CN0122SZ5b1Mhw5pzVmC1_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i2/O1CN014Q8zGQ1Mhw5oSEnHO_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i2/O1CN01nvmnpM1Mhw5pXg43A_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i3/O1CN01ubRjCv1Mhw5pzVdvN_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i3/O1CN01G25uAR1Mhw5pzUyLa_!!*******************-53-fleamarket.heic\",\"width\":\"864\"},{\"height\":\"1920\",\"image\":\"http://img.alicdn.com/bao/uploaded/i1/O1CN01Ndt4L01Mhw5nt4Bss_!!*******************-53-fleamarket.heic\",\"width\":\"864\"}]}},\"customIcons\":[{\"icon\":\"https://gw.alicdn.com/imgextra/i2/O1CN01TsFkDX20ToHXwZ7Gw_!!6000000006851-2-tps-144-144.png\",\"key\":\"idlefish-assistant\",\"title\":\"帮助与客服\",\"type\":\"link\",\"url\":\"https://h5.m.goofish.com/wow/moyu/moyu-project/customer-service-hall/pages/home?loadingVisible=false&fromType=ITEM_DETAIL&from=kVNljbMN5B&item_id=************\"}],\"disabledChannel\":\"fish-assistant\",\"sceneId\":\"************\",\"sceneType\":\"detail\",\"shareMessageCard\":{\"coverImg\":\"\",\"subTitle\":\"\",\"title\":\"\"},\"url\":\"fleamarket://awesome_detail?itemId=************&hitNativeDetail=true&flutter=true&needNotPreGet=true&ignoreMiddle=true&id=************\",\"version\":2}"
            },
            "templateId": 0,
            "worthBuySimilarFeeds": false,
            "trackParams": {
                "itemId": "************",
                "sellerId": "**********",
                "idlelabel_serviceUtParams": "[]",
                "buyerBucketId": "6",
                "sellerBucketId": "17",
                "idlelabel_bucketId": "4",
                "idlelabel_unShowTrackParams": "{}"
            },
            "userInputTopics": [],
            "soldPrice": "1",
            "itemLabelExtList": [
                {
                    "channelCateId": *********,
                    "labelId": "186465948",
                    "labelType": "common",
                    "from": "newPublishChoice",
                    "text": "电子资料",
                    "properties": "-10000##分类:*********##电子资料"
                }
            ],
            "tradeBanners": [],
            "browseCnt": 462,
            "priceRelativeTags": [
                {
                    "borderColor": "#FFFFFF",
                    "trackParams": {
                        "tagType": "baoyou"
                    },
                    "bgColor": "#FFFFFF",
                    "text": "包邮",
                    "additionMap": {},
                    "textColor": "#1F1F1F",
                    "order": 99
                }
            ],
            "priceUnit": "",
            "imageInfos": [
                {
                    "major": true,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi3%2FO1CN01F8jlJc1Mhw5qVtAU6_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01F8jlJc1Mhw5qVtAU6_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi4%2FO1CN01v0sJD01Mhw5rGkYMn_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i4/O1CN01v0sJD01Mhw5rGkYMn_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi1%2FO1CN0122SZ5b1Mhw5pzVmC1_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN0122SZ5b1Mhw5pzVmC1_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi2%2FO1CN014Q8zGQ1Mhw5oSEnHO_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN014Q8zGQ1Mhw5oSEnHO_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi2%2FO1CN01nvmnpM1Mhw5pXg43A_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i2/O1CN01nvmnpM1Mhw5pXg43A_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi3%2FO1CN01ubRjCv1Mhw5pzVdvN_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01ubRjCv1Mhw5pzVdvN_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi3%2FO1CN01G25uAR1Mhw5pzUyLa_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i3/O1CN01G25uAR1Mhw5pzUyLa_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                },
                {
                    "major": false,
                    "photoSearchUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/idle-photo-search/pages/home?kun=true&wh_ttid=native&opaque=false&extra=%7B%22imageInfo%22%3A%7B%22bizCode%22%3A%22idledetail%22%2C%22reqFromPage%22%3A%22idle_detail%22%2C%22source%22%3A%22itempic%22%2C%22url%22%3A%22http%3A%2F%2Fimg.alicdn.com%2Fbao%2Fuploaded%2Fi1%2FO1CN01Ndt4L01Mhw5nt4Bss_%21%21*******************-53-fleamarket.heic_640x640q90.jpg%22%7D%7D",
                    "widthSize": 864,
                    "heightSize": 1920,
                    "type": 0,
                    "url": "http://img.alicdn.com/bao/uploaded/i1/O1CN01Ndt4L01Mhw5nt4Bss_!!*******************-53-fleamarket.heic",
                    "extraInfo": {
                        "raw": "false",
                        "isT": "false",
                        "isH": "false"
                    },
                    "labels": []
                }
            ],
            "titleIsUserInput": false,
            "recommendTagList": [],
            "tbSupportTrade": true,
            "spuBottomBarItem": {
                "trackParams": {
                    "bucket_id": "2",
                    "category_id": "*********",
                    "item_id": "************"
                },
                "title": "卖同款",
                "targetUrl": "fleamarket://simple_post?sameItemId=************&sourceId=DetailSimilarSellBar"
            },
            "croControl": [],
            "itemId": ************,
            "GMT_CREATE_DATE_KEY": "2025-06-15 22:30:12",
            "charitableItem": false,
            "uiItemServiceDOList": [],
            "minPrice": "1",
            "maxPrice": "8.8",
            "pcSupportTrade": true,
            "commonTags": [
                {
                    "borderColor": "ECF9FF",
                    "bgColor": "ECF9FF",
                    "text": "包邮",
                    "additionMap": {},
                    "textColor": "0DAEFF",
                    "order": 99
                }
            ],
            "desc": "Compass少儿英语听力教材 Discovering/Activating/Expanding/Building Skills for Listening全套听力训练高清全套惊喜价更划算\n\n[1]Discovering Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿➕答案  A\n\n[2]Activating Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿（无答案）08\n\n[3]Expanding Skills for Listening 1,2,3级别学生用书pdf+原版音频+听力文稿➕答案   01\n\n4️⃣Building Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿➕答案",
            "descTagColor": "#FFF58300",
            "itemType": "detailCommonBuy",
            "simpleItem": true,
            "skuList": [
                {
                    "features": {
                        "sku_order_num": "0",
                        "item_dim": "0",
                        "idlePvPairs": "228680331#颜色^-1#试听音频",
                        "idleOriginalQuantity": "979"
                    },
                    "inventoryId": 1174482701540183734,
                    "itemId": ************,
                    "price": 100,
                    "priceInCent": 100,
                    "propertyList": [
                        {
                            "actualValueText": "试听音频",
                            "enabled": true,
                            "propertyId": -1,
                            "propertySortOrder": 1,
                            "propertyText": "颜色",
                            "status": 0,
                            "string": "-1:-1",
                            "valueId": -1,
                            "valueSortOrder": 0,
                            "valueText": "试听音频"
                        }
                    ],
                    "quantity": 979,
                    "sellerId": **********,
                    "skuId": 6002893426515
                },
                {
                    "features": {
                        "sku_order_num": "1",
                        "item_dim": "0",
                        "idlePvPairs": "228680331#颜色^0#全部打包",
                        "idleOriginalQuantity": "988"
                    },
                    "inventoryId": 1174482756904724441,
                    "itemId": ************,
                    "price": 880,
                    "priceInCent": 880,
                    "propertyList": [
                        {
                            "actualValueText": "全部打包",
                            "enabled": true,
                            "propertyId": -1,
                            "propertySortOrder": 1,
                            "propertyText": "颜色",
                            "status": 0,
                            "string": "-1:-3",
                            "valueId": -3,
                            "valueSortOrder": 1,
                            "valueText": "全部打包"
                        }
                    ],
                    "quantity": 988,
                    "sellerId": **********,
                    "skuId": 5889317205147
                }
            ],
            "transportFee": "0.00",
            "itemStatusStr": "在线",
            "richTextDesc": "{\"type\":null,\"children\":[{\"type\":\"Element\",\"children\":[{\"type\":\"Text\",\"text\":\"Compass少儿英语听力教材 Discovering/Activating/Expanding/Building Skills for Listening全套听力训练高清全套惊喜价更划算\\n\\n[1]Discovering Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿➕答案  A\\n\\n[2]Activating Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿（无答案）08\\n\\n[3]Expanding Skills for Listening 1,2,3级别学生用书pdf+原版音频+听力文稿➕答案   01\\n\\n4️⃣Building Skills for Listening 1,2,3 级别学生用书pdf+原版音频+听力文稿➕答案  \",\"properties\":{}}],\"properties\":{}}],\"properties\":{},\"version\":\"1.0\"}",
            "wantCntUnit": "人想要",
            "title": "Compass少儿英语听力教材 Discovering/Ac",
            "promotionPriceDO": {
                "reserved": false,
                "additionMap": {}
            },
            "createOrderUrl": "fleamarket://sku_layer?itemId=************&sourceInApp=detail",
            "interactFavorCnt": 0,
            "itemStatus": 0,
            "channelUmpCreateOrderUrl": "fleamarket://sku_layer?itemId=************&sourceInApp=detail",
            "charitableTag": {
                "iconHeight": "60",
                "iconWidth": "204",
                "iconUrl": "http://gw.alicdn.com/mt/TB1FjjLdkcx_u4jSZFlXXXnUFXa-204-60.png"
            },
            "descRelativeTags": [],
            "spuTopics": [],
            "defaultPrice": false,
            "quantity": 1967,
            "priceTextTags": [],
            "bargained": false,
            "collectCnt": 17,
            "favorCnt": 0,
            "reportUrl": "https://h5.m.goofish.com/wow/moyu/moyu-project/cro-report-center/pages/report-type?titleVisible=false&useCusFont=true&pageSourceCode=itemDetail&reportSubjectId=************",
            "cpvTopics": [],
            "gmtCreate": 1749997812000,
            "tradeAccessType": 1,
            "wantCnt": 38,
            "defaultPicture": false,
            "idleItemSkuList": [
                {
                    "features": {
                        "sku_order_num": "0",
                        "item_dim": "0",
                        "idlePvPairs": "228680331#颜色^-1#试听音频",
                        "idleOriginalQuantity": "979"
                    },
                    "itemId": ************,
                    "quantity": 979,
                    "sellerId": **********,
                    "price": 100,
                    "propertyList": [
                        {
                            "valueId": -1,
                            "valueText": "试听音频",
                            "propertySortOrder": 1,
                            "string": "-1:-1",
                            "valueSortOrder": 0,
                            "actualValueText": "试听音频",
                            "propertyText": "颜色",
                            "propertyId": -1,
                            "enabled": true,
                            "status": 0
                        }
                    ],
                    "inventoryId": 1174482701540183734,
                    "priceInCent": 100,
                    "skuId": 6002893426515
                },
                {
                    "features": {
                        "sku_order_num": "1",
                        "item_dim": "0",
                        "idlePvPairs": "228680331#颜色^0#全部打包",
                        "idleOriginalQuantity": "988"
                    },
                    "itemId": ************,
                    "quantity": 988,
                    "sellerId": **********,
                    "price": 880,
                    "propertyList": [
                        {
                            "valueId": -3,
                            "valueText": "全部打包",
                            "propertySortOrder": 1,
                            "string": "-1:-3",
                            "valueSortOrder": 1,
                            "actualValueText": "全部打包",
                            "propertyText": "颜色",
                            "propertyId": -1,
                            "enabled": true,
                            "status": 0
                        }
                    ],
                    "inventoryId": 1174482756904724441,
                    "priceInCent": 880,
                    "skuId": 5889317205147
                }
            ],
            "secuGuide": {
                "secuTitle": "什么是闲鱼支付宝担保交易？",
                "secuIcon": "https://img.alicdn.com/imgextra/i4/O1CN01rK5Qxk23BR5gvdj8j_!!6000000007217-2-tps-48-48.png",
                "secuContent": "担保交易",
                "secuBtmContext": "点击查看更多知识",
                "secuBtm": "请保证在闲鱼平台进行支付，否则闲鱼将无法保证您交易的安全。",
                "secuBtmUrl": "https://alimebot.goofish.com/intl/index.htm?from=vcWV25EkPP",
                "secuBody": "https://gw.alicdn.com/mt/TB1V4Q9OVXXXXa7aXXXXXXXXXXX-488-430.png"
            },
            "itemCatDTO": {
                "catId": ********,
                "rootChannelCatId": 201155301,
                "sugShow": false,
                "level3ChannelCatId": 202027703,
                "channelCatId": *********,
                "level2ChannelCatId": 202028703
            },
            "categoryId": ********,
            "liveRoomCreateOrderUrl": "fleamarket://sku_layer?itemId=************&sourceInApp=detail"
        },
        "b2cSellerDO": {
            "sellerStatisticsInfoList": [],
            "identityTags": [],
            "levelTags": [],
            "sellerBizTags": [],
            "userRegDay": 0,
            "sellerInfoTags": []
        },
        "sellerDO": {
            "aoiType": "0",
            "city": "北京",
            "signature": "不发快递 早5点-晚10.30。白天一直在线。着急的不要拍。退款的全部拉黑",
            "userRegDay": 2907,
            "hasSoldNumInteger": 1910,
            "publishCity": "北京",
            "nick": "剑桥考伴",
            "sellerStatisticsInfoList": [],
            "sellerId": **********,
            "idleFishCreditTag": {
                "iconHeight": "36",
                "trackParams": {
                    "sellerLevel": "5",
                    "pageUserId": "**********"
                },
                "iconWidth": "174",
                "iconUrl": "https://gw.alicdn.com/imgextra/i3/O1CN015a65Ua1WeP2daKLf9_!!6000000002813-2-tps-318-66.png",
                "additionMap": {},
                "lottieUrl": "https://g.alicdn.com/eva-assets/108ba29bda9b556357e1ab9d95c83bae/0.0.1/tmp/abe2a0b/69fab3b3-a2cb-4cb0-bd32-c33c3805e71a.json",
                "order": 99
            },
            "lastVisitTime": "2分钟前来过",
            "replyIn24hRatioDouble": 0.9477,
            "avgReply30dLong": 34,
            "sellerBizTags": [],
            "replyRatio24h": "94%",
            "replyInterval": "34分钟",
            "registerTime": 1502858438000,
            "portraitUrl": "http://img.alicdn.com/bao/uploaded/i4/O1CN01pRXocR1Mhw5MDCzBt_!!*******************-0-mtopupload.jpg",
            "sellerTypeString": null,
            "remarkDO": {
                "sellerDefaultRemarkCnt": 0,
                "sellerGoodRemarkCnt": 61,
                "sellerBadRemarkCnt": 3
            },
            "zhimaAuth": true,
            "sellerItems": [
                {
                    "itemId": 944607533694,
                    "link": "fleamarket://awesome_detail?itemId=944607533694&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i2/O1CN0181XpWJ1Mhw6XABRaP_!!*******************-53-fleamarket.heic",
                    "text": "¥3.68",
                    "type": "1"
                },
                {
                    "itemId": 931611679437,
                    "link": "fleamarket://awesome_detail?itemId=931611679437&hitNativeDetail=true&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i3/O1CN01Vl44wM1Mhw5aPlv9X_!!*******************-53-fleamarket.heic",
                    "text": "¥2",
                    "type": "1"
                },
                {
                    "itemId": 896733839382,
                    "link": "fleamarket://awesome_detail?itemId=896733839382&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i4/O1CN01WFHHa11Mhw4JRGoqu_!!*******************-53-fleamarket.heic",
                    "text": "¥2.88",
                    "type": "1"
                },
                {
                    "itemId": 892151790376,
                    "link": "fleamarket://awesome_detail?itemId=892151790376&hitNativeDetail=true&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i3/O1CN01uDvlsU1Mhw45dARbJ_!!*******************-53-fleamarket.heic",
                    "text": "¥1",
                    "type": "1"
                },
                {
                    "itemId": 921366625651,
                    "link": "fleamarket://awesome_detail?itemId=921366625651&hitNativeDetail=true&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i4/O1CN013sOURO1Mhw6005DuK_!!*******************-0-fleamarket.jpg",
                    "text": "¥15",
                    "type": "1"
                },
                {
                    "itemId": 957902255540,
                    "link": "fleamarket://awesome_detail?itemId=957902255540&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i3/O1CN01H6oMUF1Mhw6fuqmIs_!!*******************-0-fleamarket.jpg",
                    "text": "¥2",
                    "type": "1"
                },
                {
                    "itemId": 953636879222,
                    "link": "fleamarket://awesome_detail?itemId=953636879222&hitNativeDetail=true&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i2/O1CN01inrEIc1Mhw6VblTnV_!!*******************-53-fleamarket.heic",
                    "text": "¥8.88",
                    "type": "1"
                },
                {
                    "itemId": 936044373681,
                    "link": "fleamarket://awesome_detail?itemId=936044373681&hitNativeDetail=true&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i4/O1CN01ZuhMts1Mhw5gdK9M7_!!*******************-2-fleamarket.png",
                    "text": "¥0.8",
                    "type": "1"
                },
                {
                    "itemId": 921061460771,
                    "link": "fleamarket://awesome_detail?itemId=921061460771&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i1/O1CN01pzAdvb1Mhw5AgvzbJ_!!*******************-2-fleamarket.png",
                    "text": "¥5",
                    "type": "1"
                },
                {
                    "itemId": 908544507761,
                    "link": "fleamarket://awesome_detail?itemId=908544507761&hitNativeDetail=true&flutter=true&needNotPreGet=true&detailType=detailCommonBuy&isVideo=false&gulSource=detail",
                    "fontSize": 12.0,
                    "iconUrl": "http://img.alicdn.com/bao/uploaded/i1/O1CN01xtD7jU1Mhw4k7LAjM_!!*******************-53-fleamarket.heic",
                    "text": "¥19.8",
                    "type": "1"
                },
                {
                    "link": "fleamarket://personalPage?userid=**********",
                    "fontSize": 12.0,
                    "iconUrl": "https://gw.alicdn.com/imgextra/i2/O1CN01xb9Lx31ZLZQwXccBK_!!6000000003178-2-tps-276-276.png",
                    "type": "3"
                }
            ],
            "sellerInfoTags": [
                {
                    "iconHeight": "32",
                    "trackParams": {
                        "shop_tag_type": "desc_L3"
                    },
                    "bgColor": "#1900C8EC",
                    "iconWidth": "110",
                    "iconUrl": "https://gw.alicdn.com/imgextra/i1/O1CN017wQ9iD26KY3Hnvj7K_!!6000000007643-2-tps-165-48.png",
                    "text": "看主页眉头",
                    "type": "1002",
                    "additionMap": {},
                    "textColor": "#FF00C8EC",
                    "order": 99
                }
            ],
            "itemCount": 212,
            "zhimaLevelInfo": {},
            "uniqueName": "剑桥考伴",
            "identityTags": [
                {
                    "iconHeight": "32",
                    "trackParams": {
                        "appearTrackName": "",
                        "trackCtrlName": "Button-Shiren"
                    },
                    "iconWidth": "32",
                    "link": "https://h5.m.taobao.com/2shou/pd/realVerifyUrl.html?userId=**********&isVerify=1",
                    "iconUrl": "https://gw.alicdn.com/bao/uploaded/TB1E3aKPFXXXXcKXpXXXXXXXXXX-32-32.png",
                    "text": "实人认证已通过",
                    "type": "1",
                    "additionMap": {},
                    "order": 99
                }
            ],
            "levelTags": [
                {
                    "iconHeight": "32",
                    "trackParams": {
                        "shop_tag_type": "single_L3"
                    },
                    "iconWidth": "110",
                    "iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01TkPxMq1rTVZ1ii8pU_!!6000000005632-2-tps-165-48.png",
                    "additionMap": {},
                    "order": 99
                },
                {
                    "iconHeight": "36",
                    "trackParams": {
                        "sellerLevel": "5",
                        "pageUserId": "**********"
                    },
                    "iconWidth": "174",
                    "iconUrl": "https://gw.alicdn.com/imgextra/i3/O1CN015a65Ua1WeP2daKLf9_!!6000000002813-2-tps-318-66.png",
                    "additionMap": {},
                    "lottieUrl": "https://g.alicdn.com/eva-assets/108ba29bda9b556357e1ab9d95c83bae/0.0.1/tmp/abe2a0b/69fab3b3-a2cb-4cb0-bd32-c33c3805e71a.json",
                    "order": 99
                }
            ]
        },
        "b2cItemDO": {
            "priceRelativeTags": [],
            "benefitLabels": [],
            "priceTextTags": [],
            "benefitTags": [],
            "needCollapseTitle": false,
            "descRelativeTags": [],
            "templateId": 0,
            "activityInfo": {},
            "commonTags": [],
            "browseCnt": 0
        },
        "uiCountDownInfoDO": {},
        "b2cUiIdleDetailConfigDO": {
            "enableShowOthersSellInfoGuide": true,
            "needGroupInfo": true,
            "needItemGuideInfo": true,
            "supportTradeTips": false,
            "showSellerOtherItems": true,
            "enableShowQuickAskExp": true,
            "needFishPoolInfo": true,
            "enableShowBidInfo": true,
            "needShirenInfo": true,
            "needZhimaInfo": true,
            "enableShowSellSimple": true,
            "extendUserWantUnit": false,
            "enableNfgcService": true,
            "showManager": true,
            "extendPriceUnit": false,
            "enableJumpXyh": true,
            "extendDescItemType": false,
            "extendSellerStatisticInfo": false,
            "enableNfrService": true,
            "enableSdrService": true,
            "enableSellerVipLogo": true,
            "extendSellerInfoTags": false,
            "hit202109NewDetail": false,
            "extendSellerCertificate": false,
            "extendSellerLevelTags": false
        },
        "commerceDO": {},
        "uiTemplateDO": {},
        "brokerDO": {},
        "uiPostDO": {},
        "interactDO": {},
        "trackParams": {
            "target_type": "item",
            "idlelabel_bucketId": "4",
            "buyerOptions": "gameReportCpvShow|navBarFollowButton|newDetail|sellerReplyStatisticsInfo|nearby|dislikeReport|sellerUpgrade|nearbyLuxury|nearbyBulk|offlineMakePrice|seaMarketChat|nearbyLowValue|quickQuestionOffline|shopAvatarSuperLike|shareWxMini|goodValueShow|skuBannerShow|zxTagShow|skuRateAb|storeGroupAb|b2cItemDetail|couponSuperLink|skillCertficate|trendyHandbookDx|detailCouponSuperLink|b2cItemDetailAuction|b2cInspectionReportV2|on_skuBannerShow",
            "target_id": "************",
            "buyerId": "*********",
            "sellerInBuyOptions": "gameReportCpvShow|spuGuide|itemGuide|fromRegion|showBuyerPhoneInfoA|navBarFollowButton|newDetail|sellerReplyStatisticsInfo|nearby|dislikeReport|nearbyLuxury|sellerUpgrade|nearbyBulk|offlineMakePrice|seaMarketChat|nearbyLowValue|normalAttitude|quickQuestionOffline|shopAvatarSuperLike|shareWxMini|skuBannerShow|goodValueShow|zxTagShow|skuRateAb|storeGroupAb|b2cItemDetail|skillDataShow|couponSuperLink|trendyHandbookDx|detailCouponSuperLink|b2cItemDetailAuction|b2cInspectionReportV2",
            "detailSpiVersion": "2",
            "itemId": "************",
            "sellerOptions": "simplePub|newTagPage|myPubSameSpu|spuGuide|itemGuide|toBuyB|mtbFeeds|mtbSell|mtbHome|tagV2|tobuyListV2|tobuyQuery|recommendTagsChange|tobuyInitiative|glbV2|ignoreImageEditor|textSameSpu|cpvUnfoldPublish|tellusNewTask|guidePublishTypeB|priceReferenceTag|showResellList|spuBottomBarItem|myPublish91|publishSyncToCircle|directPublish|searchPublishEntry|textLengthDiagnosis|poiLab|themePublishBanner|myPublish92|myPubActivityEntry|myPublish932|accountGuarantee|albef_tpp|PROPERTY_TPP|spuBottomBarActionUrl",
            "sellerId": "**********",
            "detailRedirectPublishUrl": "",
            "idlelabel_serviceUtParams": "[]",
            "buyerBucketId": "6",
            "channelCatId": "*********",
            "mainPic": "http://img.alicdn.com/bao/uploaded/i3/O1CN01F8jlJc1Mhw5qVtAU6_!!*******************-53-fleamarket.heic",
            "sellerBucketId": "17",
            "buyerInSellBucketId": "2",
            "buyerInSellOptions": "simplePub|newTagPage|myPubSameSpu|toBuyB|spuGuide|itemGuide|tagV2|mtbFeeds|mtbSell|mtbHome|tobuyQuery|tobuyInitiative|glbV2|ignoreImageEditor|textSameSpu|cpvUnfoldPublish|publishGuideSeller|tellusNewTask|guidePublishTypeB|priceReferenceTag|showResellList|spuBottomBarItem|myPublish91|duplicateNotRemove|searchPublishEntry|directPublish|publishSyncToCircle|textLengthDiagnosis|poiLab|themePublishBanner|myPublish92|myPublish932|bothFreight|accountGuarantee|albef_tpp|PROPERTY_TPP|spuBottomBarActionUrl",
            "sellerInBuyBucketId": "3",
            "categoryId": "********",
            "idlelabel_unShowTrackParams": "{}"
        },
        "seafoodDO": {
            "bucket": "0",
            "userNum": "1111w",
            "goodName": "电子资料",
            "questionABTag": "card"
        },
        "configInfo": {
            "navBarFollowButton": "true",
            "skuBannerShow": "true",
            "showResellList": "true",
            "jumpXyh": "true",
            "hitIdleFishCreditAb": "true",
            "paymentGuideMask": "false"
        },
        "serverTime": "2025-08-02 12:33:53",
        "flexibleJobInfoDO": {},
        "buyerDO": {
            "buyQualificationActList": [],
            "isFirstOrderUser": false,
            "isNewUserIn7Day": false,
            "isShopUser": false,
            "favored": false,
            "IS_FIRST_ORDER_SAFE_GUARD_USER_KEY": false,
            "isSuperFavored": false,
            "buyerId": *********,
            "attentionState": 0,
            "isCollected": false
        }
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}



### 调用接口3

#### 请求参数

https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.loginuser.get/1.0/?jsv=2.7.2&appKey=********&t=*************&sign=6fd3098df5d9f13443a9364896f92bcb&v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.taobao.idlemessage.pc.loginuser.get&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.item.0.0

#### 响应参数

{
    "api": "mtop.taobao.idlemessage.pc.loginuser.get",
    "data": {
        "needDecryptKeys": [],
        "needDecryptKeysV2": [],
        "userId": *********
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "v": "1.0"
}

### 调用接口4

#### 请求参数

https://h5api.m.goofish.com/h5/mtop.gaia.nodejs.gaia.idle.data.gw.v2.index.get/1.0/?jsv=2.7.2&appKey=********&t=*************&sign=0fdc861d2993fb1e67530e409d21ce16&v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.gaia.nodejs.gaia.idle.data.gw.v2.index.get&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.item.0.0

#### 响应参数

{
    "api": "mtop.gaia.nodejs.gaia.idle.data.gw.v2.index.get",
    "v": "1.0",
    "ret": [
        "SUCCESS::调用成功"
    ],
    "data": {
        "pageNumber": 1,
        "hasMore": false,
        "serverTime": *************,
        "iOSChecked": false,
        "isFallback": false,
        "seed": {
            "packages": {},
            "modules": {},
            "hash": "4a7b3d595b32abba3732ef7c2ffe2796"
        },
        "pageConfInfo": {
            "homeLogo": {
                "logoTitle": "闲不住，上闲鱼！",
                "logo": "https://img.alicdn.com/imgextra/i3/O1CN01XP17o61iPXe2FKJGO_!!*************-2-tps-720-216.png",
                "logoLottie": "https://gw.alipayobjects.com/os/finxbff/lolita/VAGkgXErcPEchi34qYr49/lottie.json"
            },
            "sidebarFeedback": {
                "feedbackUrl": "https://survey.goofish.com/apps/zhiliao/jrEXykeWA",
                "feedbackTitle": "反馈"
            }
        }
    }
}

### 调用接口5

#### 请求参数

https://h5api.m.goofish.com/h5/mtop.idle.web.user.page.nav/1.0/?jsv=2.7.2&appKey=********&t=*************&sign=5a000e348d43ccdaa5bcf69fb634a440&v=1.0&type=originaljson&accountSite=xianyu&dataType=json&timeout=20000&api=mtop.idle.web.user.page.nav&sessionOption=AutoLoginOnly&spm_cnt=a21ybx.item.0.0

#### 响应参数

{
    "api": "mtop.idle.web.user.page.nav",
    "data": {
        "module": {
            "base": {
                "buyerNotPayCount": 0,
                "purchaseCount": 350,
                "displayName": "tbNick_s1e43",
                "avatar": "http://gtms03.alicdn.com/tps/i3/TB1LFGeKVXXXXbCaXXX07tlTXXX-200-200.png",
                "soldCount": 7,
                "followers": "0",
                "following": "52",
                "collectionCount": 668
            }
        }
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "traceId": "215042e817541092336424049e1572",
    "v": "1.0"
}

