#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼详情页数据处理器 - 精简版（影刀专用）
作者: AI Assistant
功能: 处理详情页数据并更新Excel文件中对应的商品信息
"""

import json
import pandas as pd
import os
import time
import shutil
import tempfile
from datetime import datetime
from typing import Dict, List
import logging

# 配置
OUTPUT_BASE_DIR = r"D:\AppData\SelfSync\Code\Python\Tool\toolProject\xianyu\download"
EXCEL_DIR = os.path.join(OUTPUT_BASE_DIR, "excel")
LOG_DIR = os.path.join(OUTPUT_BASE_DIR, "logs")

def ensure_dirs():
    """确保目录存在"""
    os.makedirs(EXCEL_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

def setup_logger(product_id="default"):
    """设置日志"""
    logger = logging.getLogger(f'detail_{product_id}')
    logger.handlers.clear()
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件
    ensure_dirs()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(LOG_DIR, f"detail_{product_id}_{timestamp}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger

def extract_product_id_from_url(detail_url):
    """从详情页URL中提取商品ID"""
    try:
        import re
        # 匹配 id=数字 的模式
        match = re.search(r'id=(\d+)', detail_url)
        if match:
            return match.group(1)
        return None
    except:
        return None

def extract_detail_data(detail_response, detail_url=None):
    """从详情页响应中提取数据"""
    try:
        # 解析响应数据
        if isinstance(detail_response, str):
            data = json.loads(detail_response)
        else:
            data = detail_response

        # 获取body数据
        body = data.get('body', {})
        if isinstance(body, str):
            body = json.loads(body)

        # 获取cardList
        card_list = body.get('data', {}).get('cardList', [])
        if not card_list:
            return {}

        detail_data = {}

        # 如果提供了URL，从URL中提取商品ID
        if detail_url:
            url_product_id = extract_product_id_from_url(detail_url)
            if url_product_id:
                detail_data['商品ID'] = url_product_id

        # 查找商品详情卡片
        for card in card_list:
            if card.get('cardType') == 100005:
                card_data = card.get('cardData', {})

                # 如果没有从URL提取到ID，尝试从响应中提取
                if '商品ID' not in detail_data and card_data.get('itemId'):
                    detail_data['商品ID'] = card_data['itemId']

                if card_data.get('area'):
                    detail_data['详情地区'] = card_data['area']

                # 用户信息
                user_info = card_data.get('user', {})
                if user_info.get('userNick'):
                    detail_data['详情卖家昵称'] = user_info['userNick']

                # 发布时间
                publish_time = card_data.get('clickParam', {}).get('args', {}).get('publishTime')
                if publish_time:
                    try:
                        timestamp = int(publish_time) / 1000
                        publish_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        detail_data['详情发布时间'] = publish_date
                    except:
                        pass

                # 解析标签
                fish_tags = card_data.get('fishTags', {})
                for region_data in fish_tags.values():
                    for tag in region_data.get('tagList', []):
                        tag_data = tag.get('data', {})
                        label_id = tag_data.get('labelId')
                        content = tag_data.get('content', '')

                        if label_id and content:
                            tag_map = {
                                '9': '详情想要人数',
                                '13': '详情包邮',
                                '41': '详情原价',
                                '468': '详情关注过',
                                '919': '详情卖家信用',
                                '824': '详情回头客',
                                '598': '详情降价信息',
                                '1017': '详情可小刀'
                            }

                            if label_id in tag_map:
                                if label_id in ['13', '468', '1017']:
                                    detail_data[tag_map[label_id]] = '是'
                                else:
                                    detail_data[tag_map[label_id]] = content

                break

        return detail_data

    except Exception as e:
        return {}

def find_excel_file(excel_path_or_name):
    """查找Excel文件"""
    if os.path.isabs(excel_path_or_name) and os.path.exists(excel_path_or_name):
        return excel_path_or_name

    if not os.path.isabs(excel_path_or_name):
        excel_path = os.path.join(EXCEL_DIR, excel_path_or_name)
        if os.path.exists(excel_path):
            return excel_path

        # 查找匹配文件
        if os.path.exists(EXCEL_DIR):
            for file in os.listdir(EXCEL_DIR):
                if file.endswith('.xlsx') and excel_path_or_name.replace('.xlsx', '') in file:
                    return os.path.join(EXCEL_DIR, file)

    return None

def update_excel_safe(excel_path, product_id, detail_data, logger):
    """安全更新Excel文件"""
    max_retries = 5

    try:
        # 读取Excel文件
        for attempt in range(max_retries):
            try:
                df = pd.read_excel(excel_path, engine='openpyxl')
                logger.info(f"读取Excel成功，共 {len(df)} 行")
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    logger.warning(f"文件被占用，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                else:
                    logger.error("文件被占用，无法读取")
                    return False
        else:
            return False

        # 查找商品行
        product_row_index = None
        for index, row in df.iterrows():
            if str(row['商品ID']) == str(product_id):
                product_row_index = index
                break

        if product_row_index is None:
            logger.warning(f"未找到商品ID: {product_id}")
            return False

        logger.info(f"找到商品记录，行号: {product_row_index + 2}")

        # 更新数据
        updated_fields = []
        for field, value in detail_data.items():
            if field not in df.columns:
                df[field] = ''
                logger.info(f"添加新列: {field}")

            df.at[product_row_index, field] = value
            updated_fields.append(field)

        # 创建临时文件
        temp_dir = os.path.dirname(excel_path)
        temp_file = os.path.join(temp_dir, f"temp_{os.path.basename(excel_path)}")

        # 保存到临时文件
        with pd.ExcelWriter(temp_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='商品数据')

            # 设置列宽
            worksheet = writer.sheets['商品数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        # 替换原文件
        for attempt in range(max_retries):
            try:
                backup_file = excel_path + ".backup"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                shutil.copy2(excel_path, backup_file)
                shutil.move(temp_file, excel_path)
                if os.path.exists(backup_file):
                    os.remove(backup_file)

                logger.info(f"更新成功，更新了 {len(updated_fields)} 个字段")
                return True

            except PermissionError:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3
                    logger.warning(f"文件被占用，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                else:
                    logger.error("文件被占用，无法更新")
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                    return False

        return False

    except Exception as e:
        logger.error(f"更新Excel失败: {e}")
        return False

# ==================== 影刀专用函数 ====================

def yingdao_process_detail(detail_response, excel_path_or_name, product_id=None, detail_url=None):
    """影刀专用：处理单个商品详情

    参数:
        detail_response: 详情页响应数据
        excel_path_or_name: Excel文件路径或名称
        product_id: 商品ID（可选，如果不提供会从detail_url中提取）
        detail_url: 详情页URL（可选，用于提取商品ID）
    """
    try:
        # 如果没有提供product_id，尝试从URL中提取
        if not product_id and detail_url:
            product_id = extract_product_id_from_url(detail_url)

        if not product_id:
            product_id = "unknown"

        logger = setup_logger(product_id)
        logger.info(f"开始处理商品详情: {product_id}")

        if detail_url:
            logger.info(f"详情页URL: {detail_url}")

        # 查找Excel文件
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            logger.error(f"未找到Excel文件: {excel_path_or_name}")
            return False

        logger.info(f"Excel文件: {excel_path}")

        # 提取详情数据
        detail_data = extract_detail_data(detail_response, detail_url)
        if not detail_data:
            logger.warning("详情数据解析失败")
            return False

        # 从详情数据中获取实际的商品ID（优先使用从URL提取的ID）
        actual_product_id = detail_data.get('商品ID', product_id)
        logger.info(f"实际商品ID: {actual_product_id}")
        logger.info(f"提取到 {len(detail_data)} 个字段: {list(detail_data.keys())}")

        # 更新Excel
        success = update_excel_safe(excel_path, actual_product_id, detail_data, logger)

        if success:
            logger.info(f"商品详情处理成功: {actual_product_id}")
        else:
            logger.error(f"商品详情处理失败: {actual_product_id}")

        return success

    except Exception as e:
        if 'logger' in locals():
            logger.error(f"处理异常: {e}")
        return False

def yingdao_batch_process_details(detail_list, excel_path_or_name):
    """影刀专用：批量处理详情页数据

    参数:
        detail_list: 详情数据列表，每个元素包含:
            - 'product_id': 商品ID（可选）
            - 'detail_response': 详情页响应数据
            - 'detail_url': 详情页URL（可选，用于提取商品ID）
        excel_path_or_name: Excel文件路径或名称
    """
    try:
        logger = setup_logger("batch")
        logger.info(f"开始批量处理，共 {len(detail_list)} 个商品")

        stats = {
            'total': len(detail_list),
            'success': 0,
            'failed': 0,
            'errors': []
        }

        for i, detail_item in enumerate(detail_list, 1):
            product_id = detail_item.get('product_id', '')
            detail_response = detail_item.get('detail_response', '')
            detail_url = detail_item.get('detail_url', '')

            logger.info(f"处理第 {i}/{len(detail_list)} 个商品: {product_id or '从URL提取'}")

            try:
                success = yingdao_process_detail(
                    detail_response,
                    excel_path_or_name,
                    product_id,
                    detail_url
                )
                if success:
                    stats['success'] += 1
                    logger.info(f"商品处理成功")
                else:
                    stats['failed'] += 1
                    stats['errors'].append(f"商品处理失败")
                    logger.error(f"商品处理失败")
            except Exception as e:
                stats['failed'] += 1
                error_msg = f"商品处理异常: {e}"
                stats['errors'].append(error_msg)
                logger.error(error_msg)

        logger.info(f"批量处理完成 - 总数: {stats['total']}, 成功: {stats['success']}, 失败: {stats['failed']}")
        return stats

    except Exception as e:
        if 'logger' in locals():
            logger.error(f"批量处理异常: {e}")
        return {'total': 0, 'success': 0, 'failed': 0, 'errors': [str(e)]}

def yingdao_get_pending_products(excel_path_or_name):
    """影刀专用：获取需要处理详情的商品列表"""
    try:
        excel_path = find_excel_file(excel_path_or_name)
        if not excel_path:
            return []

        df = pd.read_excel(excel_path, sheet_name='商品数据')
        pending_products = []

        for index, row in df.iterrows():
            need_details = row.get('需要详情', '否') == '是'
            has_details = row.get('详情已获取', '否') == '是'

            if need_details and not has_details:
                pending_products.append({
                    'product_id': str(row['商品ID']),
                    'title': row.get('商品标题', ''),
                    'price': row.get('价格', ''),
                    'detail_url': row.get('详情链接', '')
                })

        return pending_products

    except Exception as e:
        return []