# 闲鱼详情页数据处理器使用说明

## 概述

本文档介绍如何使用 `xianyu_detail_functions.py` 处理闲鱼详情页数据，该模块专为影刀RPA设计，提供纯函数接口。

## 文件结构

```
xianyu/
├── xianyu_functions_pure.py      # 首页数据处理（第一步）
├── xianyu_detail_functions.py    # 详情页数据处理（第二步）
└── download/                     # 固定输出目录
    ├── excel/                    # Excel文件存储目录
    └── logs/                     # 日志文件存储目录
```

## 主要功能

### 1. 详情页数据提取
- 从详情页API响应中提取商品详细信息
- 支持卖家信息、地区、商品描述、服务标签等
- 自动解析标签数据（24小时发货、包邮、无理由退货等）

### 2. Excel文件更新
- 根据商品ID匹配Excel中的记录
- 更新详情页获取的数据到对应行
- 保持原有数据不变，只添加新的详情字段

### 3. 批量处理支持
- 支持单个商品详情处理
- 支持批量商品详情处理
- 提供处理进度和结果统计

## 影刀专用函数

### 1. yingdao_process_detail()
**功能**: 处理单个商品的详情页数据

**参数**:
- `detail_response_json`: 详情页接口返回的JSON字符串
- `excel_filename`: Excel文件名（如：`闲鱼首页商品_20240102_143022.xlsx`）
- `product_id`: 商品ID

**返回值**:
```python
{
    'success': True/False,
    'message': '处理结果描述',
    'product_id': '商品ID',
    'updated_fields': ['更新的字段列表']  # 仅成功时返回
}
```

**使用示例**:
```python
# 在影刀中调用
result = yingdao_process_detail(
    detail_response_json='{"data":{"cardList":[...]}}',
    excel_filename='闲鱼首页商品_20240102_143022.xlsx',
    product_id='882560502443'
)
```

### 2. yingdao_batch_process_details()
**功能**: 批量处理多个商品的详情页数据

**参数**:
- `detail_list`: 详情数据列表，格式：
  ```python
  [
      {
          'product_id': '882560502443',
          'detail_response': '{"data":{"cardList":[...]}}'
      },
      {
          'product_id': '123456789',
          'detail_response': '{"data":{"cardList":[...]}}'
      }
  ]
  ```
- `excel_filename`: Excel文件名

**返回值**:
```python
{
    'success_count': 2,      # 成功处理数量
    'failed_count': 0,       # 失败处理数量
    'total_count': 2,        # 总处理数量
    'details': [             # 每个商品的详细结果
        {'success': True, 'product_id': '882560502443', ...},
        {'success': True, 'product_id': '123456789', ...}
    ]
}
```

### 3. yingdao_get_pending_products()
**功能**: 获取需要处理详情的商品列表

**参数**:
- `excel_filename`: Excel文件名

**返回值**:
```python
[
    {
        'product_id': '882560502443',
        'title': '商品标题',
        'price': '99.00',
        'detail_url': 'https://www.goofish.com/item?id=882560502443&categoryId=200922010'
    }
]
```

## 数据更新字段

详情页处理会在Excel中添加以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 卖家昵称 | 卖家的昵称 | "张三的小店" |
| 地区 | 商品所在地区 | "浙江 杭州" |
| 商品描述 | 商品详细描述（前200字符） | "全新未拆封，原价购买..." |
| 卖家信用 | 卖家信用等级 | "信用极好" |
| 服务标签 | 服务标签组合 | "24小时发货; 包邮; 无理由退货" |
| 原价 | 商品原价 | "¥199" |
| 详情已获取 | 是否已获取详情 | "是" |
| 详情更新时间 | 详情更新时间 | "2024-01-02 14:30:22" |

## 使用流程

### 第一步：首页数据处理
使用 `xianyu_functions_pure.py` 处理首页数据，生成Excel文件。

### 第二步：获取待处理商品
```python
# 获取需要详情的商品列表
pending_products = yingdao_get_pending_products('闲鱼首页商品_20240102_143022.xlsx')
```

### 第三步：影刀获取详情数据
在影刀中遍历商品列表，调用详情页接口获取数据。

### 第四步：处理详情数据
```python
# 单个处理
result = yingdao_process_detail(detail_json, excel_filename, product_id)

# 或批量处理
results = yingdao_batch_process_details(detail_list, excel_filename)
```

## 注意事项

1. **文件路径**: 所有文件都保存在固定路径 `D:\AppData\SelfSync\Code\Python\Tool\toolProject\xianyu\download`
2. **Excel文件**: 必须是由首页处理生成的Excel文件，包含必要的列结构
3. **商品ID匹配**: 通过商品ID精确匹配Excel中的记录
4. **数据安全**: 只更新详情相关字段，不会覆盖原有数据
5. **日志记录**: 每次处理都会生成详细的日志文件
6. **错误处理**: 提供完整的错误信息和处理状态

## 错误处理

常见错误及解决方案：

1. **Excel文件未找到**: 检查文件名是否正确，确保文件在excel目录中
2. **商品ID不匹配**: 确保传入的product_id与Excel中的商品ID一致
3. **详情数据解析失败**: 检查detail_response_json格式是否正确
4. **权限问题**: 确保有写入Excel文件的权限

## 日志文件

日志文件保存在 `logs/` 目录下，文件名格式：
- 单个处理: `详情页处理_YYYYMMDD_HHMMSS.log`
- 批量处理: `批量详情处理_YYYYMMDD_HHMMSS.log`

日志包含详细的处理过程、错误信息和统计数据。
