#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼数据处理函数 - 纯函数版本（影刀专用）
作者: AI Assistant
功能: 提供给影刀RPA调用的纯函数接口，无类定义
"""

import json
import re
import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

# 固定输出路径配置
OUTPUT_BASE_DIR = r"D:\AppData\SelfSync\Code\Python\Tool\toolProject\xianyu\download"
EXCEL_DIR = os.path.join(OUTPUT_BASE_DIR, "excel")
LOG_DIR = os.path.join(OUTPUT_BASE_DIR, "logs")

def ensure_output_dirs():
    """确保输出目录存在"""
    os.makedirs(EXCEL_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

# 全局变量
products_dict = {}  # 商品数据字典，用于去重
logger = None

def setup_logger(log_file=None):
    """设置日志配置"""
    global logger
    logger = logging.getLogger('xianyu_pure')
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s')
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保输出目录存在
        ensure_output_dirs()
        log_path = os.path.join(LOG_DIR, log_file)

        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.info(f"日志文件已设置: {log_path}")
    
    return logger

# 初始化日志
logger = setup_logger()

def extract_label_data(label_data, label_id):
    """提取标签数据"""
    try:
        for region in ['r2', 'r3']:
            if region in label_data:
                tag_list = label_data[region].get('tagList', [])
                for tag in tag_list:
                    tag_data = tag.get('data', {})
                    if str(tag_data.get('labelId')) == str(label_id):
                        return tag_data.get('content', '')
        return None
    except Exception as e:
        logger.warning(f"提取标签数据失败: {e}")
        return None

def parse_time_info(time_str):
    """解析时间信息"""
    if not time_str:
        return None
    
    # 匹配小时
    hour_match = re.search(r'(\d+)小时', time_str)
    if hour_match:
        return int(hour_match.group(1))
    
    # 匹配天数
    day_match = re.search(r'(\d+)天', time_str)
    if day_match:
        return int(day_match.group(1)) * 24
    
    # 匹配周
    week_match = re.search(r'(\d+)周', time_str)
    if week_match:
        return int(week_match.group(1)) * 24 * 7
    
    return None

def parse_want_count(want_str):
    """解析想要人数"""
    if not want_str:
        return 0
    
    match = re.search(r'(\d+)人想要', want_str)
    return int(match.group(1)) if match else 0

def parse_bargain_count(bargain_str):
    """解析小刀价人数"""
    if not bargain_str:
        return 0
    
    match = re.search(r'(\d+)人小刀价', bargain_str)
    return int(match.group(1)) if match else 0

def check_need_details(product_info):
    """检查是否需要获取详情"""
    # 检查想要人数
    want_count = product_info.get('want_count', 0)
    if want_count > 5:
        return True, f"想要{want_count}人"
    
    # 检查发布时间
    publish_hours = product_info.get('publish_within_hours')
    if publish_hours and publish_hours <= 168:  # 一周内
        if publish_hours <= 24:
            time_desc = "24小时内发布"
        elif publish_hours <= 48:
            time_desc = "48小时内发布"
        elif publish_hours <= 72:
            time_desc = "72小时内发布"
        else:
            time_desc = "1周内发布"
        return True, time_desc
    
    return False, None

def process_card_data(card_data):
    """处理单个卡片数据"""
    try:
        # 提取基本信息
        product_id = card_data.get('id', '')
        title = card_data.get('title', '')
        price_info = card_data.get('priceInfo', {})
        price = float(price_info.get('price', 0)) if price_info.get('price') else 0.0
        pic_info = card_data.get('picInfo', {})
        image_url = pic_info.get('picUrl', '')
        category_id = card_data.get('categoryId', '')

        # 组装正确的闲鱼商品链接
        detail_url = f"https://www.goofish.com/item?id={product_id}&categoryId={category_id}" if product_id and category_id else ''
        
        # 提取跟踪参数
        track_params = card_data.get('trackParams', {})
        rank_score = float(track_params.get('rankScore', 0)) if track_params.get('rankScore') else None
        user_id = track_params.get('user_id', '')
        
        # 提取标签数据
        item_label_data = card_data.get('itemLabelDataVO', {})
        label_data = item_label_data.get('labelData', {})
        
        # 提取各种标签信息
        want_str = extract_label_data(label_data, '9')  # 想要人数
        bargain_str = extract_label_data(label_data, '10')  # 小刀价人数
        hot_rank_str = extract_label_data(label_data, '11')  # 热销排名
        publish_time_str = extract_label_data(label_data, '492')  # 发布时间
        
        # 解析数值
        want_count = parse_want_count(want_str)
        bargain_count = parse_bargain_count(bargain_str)
        hot_rank = int(re.search(r'(\d+)', hot_rank_str).group(1)) if hot_rank_str and re.search(r'(\d+)', hot_rank_str) else None
        publish_within_hours = parse_time_info(publish_time_str)
        
        # 构建商品信息
        product_info = {
            'product_id': product_id,
            'title': title,
            'price': price,
            'detail_url': detail_url,
            'image_url': image_url,
            'category_id': category_id,
            'rank_score': rank_score,
            'user_id': user_id,
            'want_count': want_count,
            'bargain_count': bargain_count,
            'hot_rank': hot_rank,
            'publish_within_hours': publish_within_hours,
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 检查是否需要获取详情
        need_details, reason = check_need_details(product_info)
        product_info['need_details'] = need_details
        if reason:
            product_info['detail_reason'] = reason
        
        return product_info
        
    except Exception as e:
        logger.error(f"处理卡片数据失败: {e}")
        return None

def export_to_excel(products_data, output_file):
    """导出数据到Excel"""
    try:
        # 确保输出目录存在
        ensure_output_dirs()
        excel_path = os.path.join(EXCEL_DIR, output_file)

        logger.info(f"📄 开始Excel导出流程...")
        logger.info(f"   📊 待导出商品数量: {len(products_data)}")
        logger.info(f"   📁 目标文件: {excel_path}")
        
        # 转换数据格式
        logger.info(f"   🔄 开始转换数据格式...")
        excel_data = []
        
        for product in products_data:
            # 处理发布时间显示
            publish_display = ""
            if product.get('publish_within_hours'):
                hours = product['publish_within_hours']
                if hours <= 24:
                    publish_display = f"{hours}小时内发布"
                elif hours <= 72:
                    publish_display = f"{hours}小时内发布"
                else:
                    days = hours // 24
                    publish_display = f"{days}天内发布"
            
            # 处理标记原因
            reasons = []
            if product.get('want_count', 0) > 5:
                reasons.append(f"想要{product['want_count']}人")
            if product.get('publish_within_hours') and product['publish_within_hours'] <= 168:
                if product['publish_within_hours'] <= 24:
                    reasons.append("24小时内发布")
                elif product['publish_within_hours'] <= 48:
                    reasons.append("48小时内发布")
                elif product['publish_within_hours'] <= 72:
                    reasons.append("72小时内发布")
                else:
                    reasons.append("1周内发布")
            
            excel_row = {
                '商品ID': product.get('product_id', ''),
                '商品标题': product.get('title', ''),
                '商品价格': product.get('price', 0),
                '发布时间范围': publish_display,
                '想要人数': product.get('want_count', 0),
                '小刀价人数': product.get('bargain_count', 0),
                '热销排名': product.get('hot_rank'),
                '需要获取详情': '是' if product.get('need_details', False) else '否',
                '标记原因': '; '.join(reasons) if reasons else None,
                '商品链接': product.get('detail_url', ''),
                '主图URL': product.get('image_url', ''),
                '分类ID': product.get('category_id', ''),
                '排名分数': product.get('rank_score'),
                '用户ID': product.get('user_id', ''),
                '最后更新时间': product.get('last_updated', '')
            }
            excel_data.append(excel_row)
        
        logger.info(f"   ✅ 数据格式转换完成，共 {len(excel_data)} 行数据")
        
        # 创建DataFrame
        logger.info(f"   📊 创建DataFrame...")
        df = pd.DataFrame(excel_data)
        logger.info(f"   📊 DataFrame创建成功，形状: {df.shape}")
        
        # 写入Excel
        logger.info(f"   💾 开始写入Excel文件...")
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='商品数据', index=False)
        
        logger.info(f"   📋 数据写入工作表完成")
        
        # 格式化工作表
        logger.info(f"   🎨 开始格式化工作表...")
        from openpyxl import load_workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        wb = load_workbook(excel_path)
        ws = wb.active

        # 设置标题行样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 添加筛选器
        ws.auto_filter.ref = ws.dimensions

        wb.save(excel_path)
        logger.info(f"   🎨 工作表格式化完成")

        # 获取文件大小
        file_size = os.path.getsize(excel_path)
        logger.info(f"✅ Excel文件导出成功：{excel_path}")
        logger.info(f"   📊 文件大小: {file_size} 字节")

        return excel_path
        
    except Exception as e:
        logger.error(f"Excel导出失败: {e}")
        return False

def reset_data():
    """重置商品数据"""
    global products_dict
    old_count = len(products_dict)
    products_dict = {}
    logger.info(f"🔄 数据已重置，清除 {old_count} 个商品")
    return old_count

def get_stats():
    """获取当前统计信息"""
    global products_dict
    total_count = len(products_dict)
    marked_count = sum(1 for product in products_dict.values() if product.get('need_details', False))
    return {
        'total_count': total_count,
        'marked_count': marked_count
    }

def get_detail_ids():
    """获取需要详情的商品ID列表"""
    global products_dict
    detail_ids = [product['product_id'] for product in products_dict.values() 
                  if product.get('need_details', False)]
    return detail_ids

def process_homepage(response_body_list, output_file=None):
    """
    影刀调用 - 处理首页数据主函数
    
    参数:
    - response_body_list: 影刀返回的响应体列表
    - output_file: 输出文件名（可选）
    
    返回:
    - 处理结果信息
    """
    global products_dict, logger
    
    # 设置默认输出文件名
    if not output_file:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'闲鱼首页商品_{timestamp}.xlsx'
    
    # 设置日志文件（保存到logs目录）
    if output_file:
        excel_basename = os.path.splitext(os.path.basename(output_file))[0]
        log_file = f"{excel_basename}.log"
        logger = setup_logger(log_file)
    
    logger.info(f"🎯 开始处理首页数据")
    logger.info(f"   📊 响应体数量: {len(response_body_list) if response_body_list else 0}")
    logger.info(f"   📁 输出文件: {output_file}")
    logger.info(f"   📋 当前商品数: {len(products_dict)}")
    
    # 统计信息
    stats = {
        'total_responses': 0,
        'total_cards': 0,
        'valid_products': 0,
        'new_products': 0,
        'updated_products': 0,
        'marked_products': 0,
        'skipped_responses': 0,
        'skipped_cards': 0,
        'parse_errors': 0
    }
    
    # 处理每个响应体
    for i, response_body in enumerate(response_body_list, 1):
        try:
            logger.info(f"📦 处理第 {i}/{len(response_body_list)} 个响应体...")
            stats['total_responses'] += 1
            
            # 检查响应体结构
            if not isinstance(response_body, dict):
                logger.warning(f"   ⚠️  响应体 {i} 不是字典格式，跳过")
                stats['skipped_responses'] += 1
                continue
            
            # 提取卡片列表
            body = response_body.get('body', {})
            if isinstance(body, str):
                try:
                    body = json.loads(body)
                except:
                    pass
            
            data = body.get('data', {})
            card_list = data.get('cardList', [])
            
            logger.info(f"   📦 响应体 {i} 包含 {len(card_list)} 个卡片")
            stats['total_cards'] += len(card_list)
            
            # 处理每个卡片
            for card in card_list:
                try:
                    # 只处理商品卡片
                    if card.get('cardType') != 1003:
                        stats['skipped_cards'] += 1
                        continue
                    
                    card_data = card.get('cardData', {})
                    if not card_data:
                        stats['skipped_cards'] += 1
                        continue
                    
                    # 处理商品数据
                    product_info = process_card_data(card_data)
                    if not product_info:
                        stats['parse_errors'] += 1
                        continue
                    
                    product_id = product_info['product_id']
                    
                    # 检查是否已存在（去重）
                    if product_id in products_dict:
                        # 更新现有商品
                        products_dict[product_id] = product_info
                        stats['updated_products'] += 1
                        logger.info(f"       🔄 更新商品: {product_info['title'][:20]}...")
                    else:
                        # 新增商品
                        products_dict[product_id] = product_info
                        stats['new_products'] += 1
                        logger.info(f"       ➕ 新增商品: {product_info['title'][:20]}...")
                    
                    logger.info(f"          - 价格: {product_info['price']}元")
                    logger.info(f"          - 想要: {product_info['want_count']}人")
                    
                    if product_info.get('need_details'):
                        stats['marked_products'] += 1
                        logger.info(f"          - 🔖 已标记需要获取详情")
                    
                    stats['valid_products'] += 1
                    
                except Exception as e:
                    logger.error(f"       💥 处理卡片失败: {e}")
                    stats['parse_errors'] += 1
            
            logger.info(f"   📊 响应体 {i} 处理完成:")
            logger.info(f"       - 有效商品: {stats['valid_products']}")
            
        except Exception as e:
            logger.error(f"   💥 处理响应体 {i} 失败: {e}")
            stats['skipped_responses'] += 1
    
    # 输出统计信息
    logger.info("=" * 80)
    logger.info("📈 首页数据处理统计汇总:")
    logger.info(f"   📦 总响应体数量: {stats['total_responses']}")
    logger.info(f"   🏷️  总卡片数量: {stats['total_cards']}")
    logger.info(f"   ✅ 有效商品数量: {stats['valid_products']}")
    logger.info(f"   📊 去重后商品数量: {len(products_dict)}")
    logger.info(f"   ➕ 新增商品数量: {stats['new_products']}")
    logger.info(f"   🔄 更新商品数量: {stats['updated_products']}")
    logger.info(f"   🔖 标记获取详情数量: {stats['marked_products']}")
    logger.info(f"   ⏭️  跳过响应体数量: {stats['skipped_responses']}")
    logger.info(f"   ⏭️  跳过卡片数量: {stats['skipped_cards']}")
    logger.info(f"   💥 解析错误数量: {stats['parse_errors']}")
    
    # 导出Excel
    if output_file:
        logger.info(f"📄 开始导出Excel文件: {output_file}")
        excel_path = export_to_excel(list(products_dict.values()), output_file)
        if excel_path:
            logger.info(f"✅ Excel文件导出成功: {excel_path}")
            export_success = True
        else:
            logger.error(f"❌ Excel文件导出失败: {output_file}")
            export_success = False
    else:
        logger.info(f"ℹ️  未指定输出文件，跳过Excel导出")
        export_success = True
        excel_path = None

    logger.info("=" * 80)

    # 返回结果 - 直接返回Excel文件的完整路径字符串
    if export_success and excel_path:
        return excel_path  # 直接返回完整路径字符串
    else:
        return None  # 处理失败时返回None

# ==================== 影刀专用简化函数 ====================

def yingdao_process_homepage(response_body_list, output_file=None):
    """影刀专用 - 处理首页数据"""
    result = process_homepage(response_body_list, output_file)
    return result

def yingdao_get_detail_ids():
    """影刀专用 - 获取需要详情的商品ID列表"""
    return get_detail_ids()

def yingdao_reset_data():
    """影刀专用 - 重置数据状态"""
    count = reset_data()
    return count > 0

def yingdao_get_stats():
    """影刀专用 - 获取统计信息"""
    return get_stats()

# ==================== 测试函数 ====================

def test_pure_functions():
    """测试纯函数版本的功能"""
    print("🧪 开始测试纯函数版本...")

    # 测试数据
    test_data = [
        {
            "type": "xhr",
            "url": "https://h5api.m.taobao.com/h5/mtop.idle.web.xyh.item.list/1.0/",
            "status": 200,
            "body": {
                "data": {
                    "cardList": [
                        {
                            "cardType": 1003,
                            "cardData": {
                                "id": "882560502443",
                                "title": "iPhone 14 Pro Max 256G 深空黑色",
                                "priceInfo": {"price": "6800"},
                                "picInfo": {"picUrl": "https://img.alicdn.com/test.jpg"},
                                "categoryId": "200922010",
                                "trackParams": {"rankScore": "0.95", "user_id": "user123"},
                                "itemLabelDataVO": {
                                    "labelData": {
                                        "r3": {"tagList": [{"data": {"content": "8人想要", "labelId": "9"}}]},
                                        "r2": {"tagList": [{"data": {"content": "24小时内发布", "labelId": "492"}}]}
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        }
    ]

    # 测试处理
    result = yingdao_process_homepage(test_data, "test_pure_output.xlsx")
    print(f"✅ 处理结果: {result['success']}, 商品数: {result['count']}")

    # 测试统计
    stats = yingdao_get_stats()
    print(f"✅ 统计信息: 总数={stats['total_count']}, 标记数={stats['marked_count']}")

    # 测试获取详情ID
    detail_ids = yingdao_get_detail_ids()
    print(f"✅ 详情ID: {detail_ids}")

    print("🎉 纯函数版本测试完成！")
    return True

if __name__ == "__main__":
    test_pure_functions()
