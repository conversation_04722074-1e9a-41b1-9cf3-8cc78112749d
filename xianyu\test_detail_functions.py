#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试详情处理函数
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xianyu_detail_functions import yingdao_process_detail, yingdao_batch_process_details

def test_detail_processing():
    """测试详情处理功能"""
    print("🧪 开始测试详情处理功能...")
    
    # 测试数据 - 模拟影刀详情接口返回的数据
    test_detail_response = {
        'type': 'XHR',
        'url': 'https://h5api.m.goofish.com/h5/mtop.taobao.idle.item.web.recommend.list/1.0/',
        'status': 200,
        'body': {
            "api": "mtop.taobao.idle.item.web.recommend.list",
            "data": {
                "cardList": [
                    {
                        "cardData": {
                            "area": "福州",
                            "clickParam": {
                                "args": {
                                    "publishTime": "1752069844000",
                                    "wantNum": "19",
                                    "id": "949953422136"
                                }
                            },
                            "desc": "lan+Skillful+Third+Edition Foundation/1/2/3/4（听说+读写）共10个级别：学生书、教师书、配套音频、配套视频、Worksheet+Answer、Audio+Scripts、Video+Scrip...",
                            "fishTags": {
                                "r3": {
                                    "tagList": [
                                        {
                                            "data": {
                                                "labelId": "1017",
                                                "content": "可小刀"
                                            }
                                        },
                                        {
                                            "data": {
                                                "color": "#999999",
                                                "size": "12",
                                                "labelId": "9",
                                                "content": "19人想要"
                                            }
                                        }
                                    ]
                                },
                                "r1": {
                                    "tagList": [
                                        {
                                            "data": {
                                                "labelId": "13",
                                                "content": "freeShippingIcon"
                                            }
                                        }
                                    ]
                                },
                                "r4": {
                                    "tagList": [
                                        {
                                            "data": {
                                                "labelId": "919",
                                                "content": "卖家信用极好"
                                            }
                                        }
                                    ]
                                }
                            },
                            "itemId": "949953422136",
                            "price": "2.88",
                            "title": "Skillful+第三版+听说+读写+麦克米伦+Macmil",
                            "user": {
                                "avatar": "http://img.alicdn.com/bao/uploaded/i2/O1CN01gd3K791WQCvWtAlUa_!!*******************-0-mtopupload.jpg",
                                "userNick": "肯德鸭特工"
                            }
                        },
                        "cardType": 100005
                    }
                ]
            }
        }
    }
    
    # 测试单个商品详情处理
    print("\n🔍 测试单个商品详情处理...")
    
    # 使用之前生成的Excel文件进行测试
    excel_file = "测试首页商品.xlsx"  # 使用之前测试生成的文件
    product_id = "949953422136"
    
    print(f"📄 使用Excel文件: {excel_file}")
    print(f"🎯 目标商品ID: {product_id}")
    
    # 调用详情处理函数
    result = yingdao_process_detail(test_detail_response, excel_file, product_id)
    
    if result:
        print(f"✅ 详情处理成功!")
    else:
        print(f"❌ 详情处理失败!")
    
    # 测试批量处理
    print("\n🚀 测试批量详情处理...")
    
    batch_data = [
        {
            'response': test_detail_response,
            'product_id': product_id
        }
    ]
    
    batch_result = yingdao_batch_process_details(batch_data, excel_file)
    
    print(f"📊 批量处理结果:")
    print(f"   总数: {batch_result['total']}")
    print(f"   成功: {batch_result['success']}")
    print(f"   失败: {batch_result['failed']}")
    
    if batch_result['errors']:
        print(f"   错误: {batch_result['errors']}")
    
    print("\n🎉 详情处理功能测试完成!")
    return True

if __name__ == "__main__":
    test_detail_processing()
