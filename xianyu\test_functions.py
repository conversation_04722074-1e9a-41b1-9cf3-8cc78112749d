#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试闲鱼函数
"""

import sys
import os
sys.path.append('.')

from xianyu_functions_pure import yingdao_process_homepage

def test_homepage():
    """测试首页数据处理"""
    
    # 测试数据
    test_data = {
        "data": {
            "cardList": [
                {
                    "cardType": 1003,
                    "cardData": {
                        "itemId": "123456789",
                        "categoryId": "200922010",
                        "title": "测试商品",
                        "price": "99.00",
                        "fishTags": {
                            "r2": {
                                "tagList": [
                                    {
                                        "data": {
                                            "labelId": "492",
                                            "content": "24小时内发布"
                                        }
                                    },
                                    {
                                        "data": {
                                            "labelId": "9",
                                            "content": "5人想要"
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            ]
        }
    }
    
    import json
    test_json = json.dumps(test_data)
    
    print('测试首页数据处理...')
    result = yingdao_process_homepage(test_json, '测试首页商品.xlsx')
    print('结果:', result)

    # 检查返回的文件路径
    if result:
        print(f"\n✅ Excel文件已生成: {result}")

        # 测试详情页处理函数是否能正确接受完整路径
        print(f"\n🔍 测试详情页处理函数能否接受完整路径...")
        from xianyu_detail_functions import yingdao_get_pending_products

        # 使用完整路径测试
        try:
            pending_products = yingdao_get_pending_products(result)
            print(f"📊 使用完整路径获取待处理商品: {len(pending_products)} 个")
        except Exception as e:
            print(f"❌ 使用完整路径失败: {e}")

        # 从路径中提取文件名进行测试
        try:
            file_name = os.path.basename(result)
            pending_products2 = yingdao_get_pending_products(file_name)
            print(f"📊 使用文件名获取待处理商品: {len(pending_products2)} 个")
        except Exception as e:
            print(f"❌ 使用文件名失败: {e}")
    else:
        print(f"\n❌ 处理失败: 返回值为 None")

    # 检查文件是否生成
    download_dir = r"D:\AppData\SelfSync\Code\Python\Tool\toolProject\xianyu\download"
    excel_dir = os.path.join(download_dir, "excel")
    log_dir = os.path.join(download_dir, "logs")

    print(f'\n检查输出目录:')
    print(f'Excel目录: {excel_dir}')
    if os.path.exists(excel_dir):
        excel_files = [f for f in os.listdir(excel_dir) if f.endswith('.xlsx')]
        print(f'Excel文件: {excel_files}')
    else:
        print('Excel目录不存在')

    print(f'日志目录: {log_dir}')
    if os.path.exists(log_dir):
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        print(f'日志文件: {log_files}')
    else:
        print('日志目录不存在')

if __name__ == '__main__':
    test_homepage()
