# 闲鱼数据分析工具 - 纯函数版本

专为影刀RPA设计的闲鱼商品数据分析工具，采用纯函数架构，确保影刀能够正确识别和调用所有功能。

## 🎯 核心特性

- ✅ **纯函数架构** - 无类定义，影刀直接识别
- ✅ **智能去重** - 基于商品ID自动去重更新
- ✅ **智能标记** - 自动标记需要详情的商品
- ✅ **Excel导出** - 完整格式化的Excel文件
- ✅ **详细日志** - 完整的处理过程记录
- ✅ **状态管理** - 跨批次数据累积处理

## 📁 文件说明

- **`xianyu_functions_pure.py`** - 主程序文件（影刀调用）
- **`纯函数版本使用说明.md`** - 详细使用说明
- **`首页接口影刀.md`** - 接口分析文档
- **`商品详情页.md`** - 详情页分析文档

## 🚀 快速开始

### 影刀调用示例

```python
# 1. 处理首页数据
result = yingdao_process_homepage(response_data, "商品数据.xlsx")

# 2. 获取需要详情的商品ID
detail_ids = yingdao_get_detail_ids()

# 3. 查看统计信息
stats = yingdao_get_stats()

# 4. 重置数据（可选）
yingdao_reset_data()
```

### 影刀可识别的函数

- `yingdao_process_homepage()` - 处理首页数据
- `yingdao_get_detail_ids()` - 获取详情商品ID
- `yingdao_reset_data()` - 重置数据状态
- `yingdao_get_stats()` - 获取统计信息

## 📊 输出文件

- **Excel文件** - 保存到当前工作目录
- **日志文件** - 与Excel同名的.log文件
- **自动格式化** - 包含筛选器和样式

## 🔧 标记规则

自动标记需要获取详情的商品：
- 想要人数 > 5人
- 发布时间 ≤ 24/48/72小时
- 发布时间 ≤ 1周

## 📖 详细文档

请查看 `纯函数版本使用说明.md` 获取完整的使用指南和函数说明。

## 🧪 测试

```bash
# 运行测试
python xianyu_functions_pure.py
```

## ⚠️ 注意事项

- 请遵守闲鱼平台的使用条款
- 数据仅供学习和研究使用
- Excel和日志文件保存到当前工作目录

---

*纯函数版本 - 专为影刀RPA优化*
