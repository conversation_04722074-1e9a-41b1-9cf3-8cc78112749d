# 闲鱼数据分析工具设计方案 (影刀版本)

## 1. 项目概述

基于影刀RPA操作浏览器获取闲鱼API接口数据，开发数据解析和Excel导出功能，用于分析店铺热销产品趋势。

**分工方案**：
- **影刀负责**: 浏览器操作、接口数据获取、反爬虫处理
- **Python函数负责**: 数据解析、筛选、Excel导出

## 2. 关键发现：商品详情页包含发布时间

### 2.1 数据源对比分析

经过详细分析，发现两个API接口的数据完整性差异：

| 数据字段 | 列表页API | 详情页API | 获取方式 |
|---------|-----------|-----------|----------|
| 商品ID | ✅ `cardData.id` | ✅ `cardData.itemId` | 直接获取 |
| 商品标题 | ✅ `cardData.title` | ✅ `cardData.title` | 直接获取 |
| 商品价格 | ✅ `cardData.priceInfo.price` | ✅ `cardData.price` | 直接获取 |
| 商品链接 | ✅ `cardData.detailUrl` | ✅ `cardData.targetUrl` | 直接获取 |
| 主图URL | ✅ `cardData.picInfo.picUrl` | ✅ `cardData.image.url` | 直接获取 |
| 想要人数 | ✅ 标签解析 | ✅ 标签解析 | 解析fishTags |
| **发布时间** | ❌ **缺失** | ✅ `clickParam.args.publishTime` | **关键字段！** |
| **店铺名** | ❌ **缺失** | ✅ `cardData.user.userNick` | **店铺昵称！** |
| 商品描述 | ❌ **缺失** | ✅ `cardData.desc` | 商品详细描述 |
| 地区信息 | ❌ **缺失** | ✅ `cardData.area` | 卖家地区 |

### 2.2 发布时间字段分析

**关键发现**: 商品详情页API响应中包含 `publishTime` 字段！

- **字段位置**: `clickParam.args.publishTime`
- **数据格式**: 13位毫秒级时间戳
- **示例数据**:
  - `"1753938145000"` → `2025-07-31 13:02:25`
  - `"1728015558000"` → `2024-10-04 12:19:18`
  - `"1703995957000"` → `2023-12-31 12:12:37`

这解决了**24/48/72小时筛选**的核心需求！

## 3. 完整工作流程设计

### 3.1 流程方案选择

基于数据完整性分析，推荐采用**两步法**：

**方案A：两步法（推荐）**
1. **第一步**：获取商品列表，初步筛选
2. **第二步**：获取详情页数据，精确筛选和导出

**方案B：直接法**
- 直接遍历所有商品详情页（数据完整但操作较慢）

### 3.2 推荐流程：两步法详细设计

#### 步骤1：商品列表初筛
```
影刀操作：
1. 打开闲鱼店铺页面
2. 滚动加载获取所有商品列表数据
3. 拦截 mtop.idle.web.xyh.item.list API响应

Python处理：
1. 解析列表页JSON数据
2. 提取基础信息（ID、标题、价格、想要人数等）
3. 初步筛选（想要人数 >= 阈值）
4. 返回需要获取详情的商品ID列表
```

#### 步骤2：商品详情精筛
```
影刀操作：
1. 根据商品ID列表，逐个点击商品详情页
2. 拦截 mtop.taobao.idle.item.web.recommend.list API响应
3. 获取包含发布时间的完整数据

Python处理：
1. 解析详情页JSON数据
2. 提取完整信息（发布时间、店铺名、描述等）
3. 精确筛选（24/48/72小时、价格区间等）
4. 导出Excel文件
```

## 4. 影刀操作指南

### 4.1 第一步：商品列表获取

```javascript
// 影刀脚本伪代码 - 步骤1
function getProductList() {
    let productListData = [];
    let hasMoreData = true;

    // 滚动加载所有商品
    while (hasMoreData) {
        // 滚动到页面底部
        scrollToBottom();
        wait(2000);

        // 拦截API响应
        let apiResponse = interceptApiResponse('mtop.idle.web.xyh.item.list');
        if (apiResponse && apiResponse.data && apiResponse.data.cardList) {
            productListData.push(apiResponse);

            // 检查是否还有更多数据
            hasMoreData = checkHasMoreData(apiResponse);
        } else {
            hasMoreData = false;
        }
    }

    // 调用Python函数进行初筛
    let targetProductIds = callPythonFunction('filter_product_list', {
        data: productListData,
        filters: {
            want_count_min: 5,  // 至少5人想要
            price_max: 100      // 价格不超过100元
        }
    });

    return targetProductIds;
}
```

### 4.2 第二步：商品详情获取

```javascript
// 影刀脚本伪代码 - 步骤2
function getProductDetails(productIds) {
    let detailDataList = [];

    for (let productId of productIds) {
        // 点击商品进入详情页
        clickProductById(productId);
        wait(3000);  // 等待页面加载

        // 拦截详情页API响应
        let detailResponse = interceptApiResponse('mtop.taobao.idle.item.web.recommend.list');
        if (detailResponse) {
            detailDataList.push({
                productId: productId,
                detailData: detailResponse
            });
        }

        // 关闭详情页，返回列表页
        closeCurrentTab();
        wait(1000);
    }

    // 调用Python函数进行最终处理
    let result = callPythonFunction('process_product_details', {
        data: detailDataList,
        filters: {
            publish_hours: 24,      // 24小时内发布
            want_count_min: 5,      // 至少5人想要
            price_max: 100          // 价格不超过100元
        },
        output_file: `闲鱼热销产品_${getCurrentDate()}.xlsx`
    });

    return result;
}
```

## 5. Python函数核心代码

### 5.1 主要函数接口

```python
def filter_product_list(data, filters=None):
    """
    第一步：筛选商品列表，返回需要获取详情的商品ID

    参数:
    - data: 商品列表API响应数据
    - filters: 初筛条件

    返回:
    - 商品ID列表
    """
    pass

def process_product_details(data, filters=None, output_file=None):
    """
    第二步：处理商品详情数据并导出Excel

    参数:
    - data: 商品详情API响应数据列表
    - filters: 精筛条件
    - output_file: 输出文件名

    返回:
    - 处理结果信息
    """
    pass

def parse_product_item(card_data, is_detail_page=False):
    """
    解析单个商品数据

    参数:
    - card_data: 商品数据
    - is_detail_page: 是否为详情页数据

    返回:
    - 解析后的商品信息字典
    """
    pass

def extract_fish_tags(fish_tags):
    """
    提取fishTags标签信息

    参数:
    - fish_tags: fishTags数据

    返回:
    - 标签信息字典 {want_count, bargain_count, hot_rank}
    """
    pass

def filter_by_publish_time(publish_time_ms, hours_limit):
    """
    根据发布时间筛选

    参数:
    - publish_time_ms: 发布时间毫秒时间戳
    - hours_limit: 小时限制 (24/48/72)

    返回:
    - 是否符合条件
    """
    pass
```

### 5.2 核心代码逻辑

#### 第一步：商品列表筛选函数
```python
import json
import re
from datetime import datetime, timedelta

def filter_product_list(data, filters=None):
    """第一步：筛选商品列表"""
    if not filters:
        filters = {}

    target_product_ids = []

    for api_response in data:
        if not api_response.get('data', {}).get('cardList'):
            continue

        for card_item in api_response['data']['cardList']:
            card_data = card_item.get('cardData', {})

            # 解析基础信息
            product_id = card_data.get('id')
            title = card_data.get('title', '')
            price = float(card_data.get('priceInfo', {}).get('price', 0))

            # 解析标签信息
            labels = extract_fish_tags(card_data.get('itemLabelDataVO', {}).get('labelData', {}))
            want_count = labels.get('want_count', 0)

            # 应用筛选条件
            if (want_count >= filters.get('want_count_min', 0) and
                price <= filters.get('price_max', float('inf')) and
                price >= filters.get('price_min', 0)):

                target_product_ids.append(product_id)

    return target_product_ids
```

#### 第二步：商品详情处理函数
```python
def process_product_details(data, filters=None, output_file=None):
    """第二步：处理商品详情数据"""
    if not filters:
        filters = {}

    processed_products = []

    for item in data:
        product_id = item.get('productId')
        detail_data = item.get('detailData', {})

        # 解析详情页数据
        for card_item in detail_data.get('data', {}).get('cardList', []):
            card_data = card_item.get('cardData', {})
            click_param = card_data.get('clickParam', {}).get('args', {})

            # 提取关键信息
            product_info = {
                'product_id': card_data.get('itemId'),
                'title': card_data.get('title', ''),
                'price': float(card_data.get('price', 0)),
                'publish_time': click_param.get('publishTime'),
                'shop_name': card_data.get('user', {}).get('userNick', ''),
                'description': card_data.get('desc', ''),
                'area': card_data.get('area', ''),
                'target_url': card_data.get('targetUrl', ''),
                'image_url': card_data.get('image', {}).get('url', ''),
                'user_avatar': card_data.get('user', {}).get('avatar', '')
            }

            # 解析标签信息
            fish_tags = card_data.get('fishTags', {})
            labels = extract_fish_tags(fish_tags)
            product_info.update(labels)

            # 应用筛选条件
            if apply_filters(product_info, filters):
                processed_products.append(product_info)

    # 导出Excel
    if output_file and processed_products:
        export_to_excel(processed_products, output_file)

    return {
        'success': True,
        'count': len(processed_products),
        'file': output_file,
        'products': processed_products
    }
```

#### 标签解析函数
```python
def extract_fish_tags(fish_tags):
    """提取fishTags标签信息"""
    result = {
        'want_count': 0,
        'bargain_count': 0,
        'hot_rank': None
    }

    # 解析r3区域标签（想要人数、小刀价）
    r3_tags = fish_tags.get('r3', {}).get('tagList', [])
    for tag in r3_tags:
        content = tag.get('data', {}).get('content', '')
        label_id = tag.get('data', {}).get('labelId', '')

        if label_id == '9' and '人想要' in content:
            # 提取"X人想要"中的数字
            match = re.search(r'(\d+)人想要', content)
            if match:
                result['want_count'] = int(match.group(1))

        elif label_id == '1047' and '人小刀价' in content:
            # 提取"X人小刀价"中的数字
            match = re.search(r'(\d+)人小刀价', content)
            if match:
                result['bargain_count'] = int(match.group(1))

    # 解析r2区域标签（热销排名）
    r2_tags = fish_tags.get('r2', {}).get('tagList', [])
    for tag in r2_tags:
        content = tag.get('data', {}).get('content', '')
        if '热销第' in content and '名' in content:
            match = re.search(r'热销第(\d+)名', content)
            if match:
                result['hot_rank'] = int(match.group(1))

    return result
```

#### 时间筛选函数
```python
def apply_filters(product_info, filters):
    """应用筛选条件"""
    # 发布时间筛选
    if filters.get('publish_hours'):
        publish_time_ms = product_info.get('publish_time')
        if publish_time_ms:
            if not filter_by_publish_time(int(publish_time_ms), filters['publish_hours']):
                return False

    # 想要人数筛选
    if product_info.get('want_count', 0) < filters.get('want_count_min', 0):
        return False

    # 价格筛选
    price = product_info.get('price', 0)
    if price < filters.get('price_min', 0) or price > filters.get('price_max', float('inf')):
        return False

    # 关键词筛选
    title = product_info.get('title', '').lower()
    if filters.get('keywords'):
        if not any(keyword.lower() in title for keyword in filters['keywords']):
            return False

    if filters.get('exclude_keywords'):
        if any(keyword.lower() in title for keyword in filters['exclude_keywords']):
            return False

    return True

def filter_by_publish_time(publish_time_ms, hours_limit):
    """根据发布时间筛选"""
    publish_time = datetime.fromtimestamp(publish_time_ms / 1000)
    current_time = datetime.now()
    time_diff = current_time - publish_time

    return time_diff.total_seconds() <= hours_limit * 3600
```

## 6. Excel导出设计

### 6.1 最终Excel字段

| 列 | 字段名 | 数据来源 | 说明 |
|----|--------|----------|------|
| A | 商品ID | `cardData.itemId` | 商品唯一标识 |
| B | 商品标题 | `cardData.title` | 商品标题 |
| C | 商品价格 | `cardData.price` | 商品价格(元) |
| D | 发布时间 | `clickParam.args.publishTime` | 转换为日期格式 |
| E | 店铺名 | `cardData.user.userNick` | 店铺昵称 |
| F | 地区 | `cardData.area` | 卖家地区 |
| G | 想要人数 | fishTags解析 | "X人想要" |
| H | 小刀价人数 | fishTags解析 | "X人小刀价" |
| I | 热销排名 | fishTags解析 | "热销第X名" |
| J | 商品描述 | `cardData.desc` | 商品详细描述 |
| K | 商品链接 | `cardData.targetUrl` | 详情页链接 |
| L | 主图URL | `cardData.image.url` | 商品主图 |

### 6.2 Excel导出函数
```python
import pandas as pd
from datetime import datetime

def export_to_excel(products, filename):
    """导出数据到Excel"""
    # 转换数据格式
    excel_data = []
    for product in products:
        # 转换发布时间
        publish_time = ''
        if product.get('publish_time'):
            timestamp = int(product['publish_time']) / 1000
            publish_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

        excel_data.append({
            '商品ID': product.get('product_id', ''),
            '商品标题': product.get('title', ''),
            '商品价格': product.get('price', 0),
            '发布时间': publish_time,
            '店铺名': product.get('shop_name', ''),
            '地区': product.get('area', ''),
            '想要人数': product.get('want_count', 0),
            '小刀价人数': product.get('bargain_count', 0),
            '热销排名': product.get('hot_rank', ''),
            '商品描述': product.get('description', ''),
            '商品链接': product.get('target_url', ''),
            '主图URL': product.get('image_url', '')
        })

    # 创建DataFrame并导出
    df = pd.DataFrame(excel_data)

    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='闲鱼热销产品', index=False)

        # 格式化工作表
        worksheet = writer.sheets['闲鱼热销产品']

        # 调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    return filename
```

## 7. 完整调用示例

### 7.1 影刀完整脚本示例
```javascript
// 影刀主函数
function analyzeXianyuProducts() {
    try {
        // 第一步：获取商品列表并初筛
        console.log("开始获取商品列表...");
        let productListData = getProductList();

        let targetIds = callPythonFunction('filter_product_list', {
            data: productListData,
            filters: {
                want_count_min: 5,
                price_max: 100
            }
        });

        console.log(`初筛完成，找到 ${targetIds.length} 个目标商品`);

        // 第二步：获取商品详情并最终处理
        console.log("开始获取商品详情...");
        let detailData = getProductDetails(targetIds);

        let result = callPythonFunction('process_product_details', {
            data: detailData,
            filters: {
                publish_hours: 24,      // 24小时内发布
                want_count_min: 5,      // 至少5人想要
                price_max: 100,         // 价格不超过100元
                keywords: ["教材", "英语"]  // 包含关键词
            },
            output_file: `闲鱼热销产品_${getCurrentDate()}.xlsx`
        });

        console.log(`分析完成！共找到 ${result.count} 个符合条件的商品`);
        console.log(`Excel文件已保存：${result.file}`);

        return result;

    } catch (error) {
        console.error("分析过程出错：", error);
        return { success: false, error: error.message };
    }
}
```

这个设计方案提供了完整的工作流程、核心代码逻辑和影刀配合方案，可以实现你要求的所有功能。
